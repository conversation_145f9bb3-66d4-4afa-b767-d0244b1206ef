/**
 * @file boat_log.h
 * @brief Stub header for boat_log functionality - not needed for time test
 * 
 * This is a minimal stub to satisfy the include in pelagic.h when TARGET_VMS is defined.
 * The time test doesn't need actual boat logging functionality.
 */

#ifndef BOAT_LOG_H
#define BOAT_LOG_H

#include <stdint.h>
#include <stdbool.h>

// Stub definitions to satisfy compilation
#define BOARD_LOG_FLAG_PARKED 0

// Stub function declarations
static inline void board_log_record(uint32_t flags) {
    // Stub - do nothing
    (void)flags;
}

#endif // BOAT_LOG_H
