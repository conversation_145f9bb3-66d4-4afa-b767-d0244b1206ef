/**
 * @file settings.h
 * @brief Stub header for settings functionality - not needed for time test
 */

#ifndef SETTINGS_H
#define SETTINGS_H

#include <stdint.h>
#include <stdbool.h>

// Stub structures and variables
typedef struct {
    struct {
        uint32_t parked_time;
        uint32_t heartbeat_interval;
        uint16_t metres_allowed;
        int32_t parked_lat;
        int32_t parked_lon;
    } parking;
    uint8_t parking_use_gps;
    uint8_t persistent_boat_log_vers;
    uint8_t persistent_event_log_vers;
} settings_t;

extern settings_t settings;
extern volatile bool enable_park;

// Stub function declarations
static inline void settings_init(void) {}
static inline void settings_store(void) {}

#endif // SETTINGS_H
