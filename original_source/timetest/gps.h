/**
 * @file gps.h
 * @brief Stub header for GPS functionality - not needed for time test
 */

#ifndef GPS_H
#define GPS_H

#include <stdint.h>
#include <stdbool.h>

// Stub definitions
extern volatile bool gps_have_fix;
extern double gps_current_lat, gps_current_lon;

// Stub function declarations
static inline void gps_pin_setup(void) {}
static inline void gps_set_high_res_tracking(void) {}
static inline void gps_track_appropriately(void) {}
static inline uint32_t PDOP_INT(void) { return 0; }
static inline uint32_t distance_in_meters(double lat1, double lon1, double lat2, double lon2) { 
    (void)lat1; (void)lon1; (void)lat2; (void)lon2;
    return 0; 
}

#endif // GPS_H
