/**
 * @file accel.h
 * @brief Stub header for accelerometer functionality - not needed for time test
 */

#ifndef ACCEL_H
#define ACCEL_H

#include <stdint.h>
#include <stdbool.h>

// Stub function declarations
static inline void accel_pin_setup(void) {}
static inline bool accel_should_stay_in_nap_mode(void) { return false; }
static inline void log_accel_deviations(uint32_t event, uint32_t param) { 
    (void)event; (void)param;
}

#endif // ACCEL_H
