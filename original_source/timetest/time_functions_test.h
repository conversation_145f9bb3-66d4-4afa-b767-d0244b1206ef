/**
 * @file time_functions_test.h
 * @brief Header file for time functions test suite
 */

#ifndef TIME_FUNCTIONS_TEST_H
#define TIME_FUNCTIONS_TEST_H

#ifdef __cplusplus
extern "C" {
#endif

/**
 * @brief Run the complete time functions test suite
 * 
 * This function tests all time-related functions to determine which ones
 * are actually working and returning incrementing values.
 * 
 * @return 0 if at least one time function is working, 1 if all are broken
 */
int run_time_functions_test(void);

#ifdef __cplusplus
}
#endif

#endif // TIME_FUNCTIONS_TEST_H
