{
    // Use IntelliSense to learn about possible attributes.
    // Hover to view descriptions of existing attributes.
    // For more information, visit: https://go.microsoft.com/fwlink/?linkid=830387
    "version": "0.2.0",
    "configurations": [
        {   // Requires arm-none-eabi-gdb to be aliased to gdb-multiarch
            // Also requires running `make debug` in a terminal prior to launch
            "name": "Debug STM32L4 (OpenOCD)",
            // "showDevDebugOutput": "both",
            "type": "cortex-debug",
            "request": "launch",
            "servertype": "external",
            // "servertype": "external",
            "executable": "${workspaceFolder}/original_source/images/pelagic_vms.elf",
            "cwd": "${workspaceFolder}",
            "device": "STM32L431CC",                         // Your specific part
            "interface": "swd",                              // Most STM32 boards use SWD
            "runToEntryPoint": "main",                       // Optional, run to main
            "svdFile": "${workspaceFolder}/STM32L4x1.svd",   // Optional SVD for peripheral view
            "configFiles": [
                "${workspaceFolder}/debug.cfg",   // OpenOCD config file
            ],
            "postRestartCommands": [
              "monitor reset halt",
              "directory ${workspaceFolder}/original_source/rtx"
            ],
            // "preLaunchTask": "Start Debug Server",
            // "postDebugTask": "Stop Debug Server",
            "gdbTarget": "localhost:2331",                     // GDB server port
            "rtos": "auto",
            "swoConfig": {
                "enabled": true,
                "source": "socket",
                "swoPort": "2332",
                "swoFrequency": 4000000,
                "cpuFrequency": 4000000,
                "decoderBytes": 2,
                "decoders": [
                    {
                        "type": "console",
                        "label": "SWO",
                        "port": 0,
                        "filter": "",
                        "showTimestamp": true,
                        "showTimestampInLocalTime": false
                    }
                ]
            }
        },
        {   // Requires arm-none-eabi-gdb to be aliased to gdb-multiarch
            // Also requires running `make debug` in a terminal prior to launch
            "name": "Debug Test",
            "type": "cortex-debug",
            "request": "launch",
            "servertype": "external",
            // "servertype": "external",
            "executable": "${input:elfChoice}",
            "cwd": "${workspaceFolder}",
            "device": "STM32L431CC",                         // Your specific part
            "interface": "swd",                              // Most STM32 boards use SWD
            "runToEntryPoint": "main",                       // Optional, run to main
            "svdFile": "${workspaceFolder}/STM32L4x1.svd",   // Optional SVD for peripheral view
            "configFiles": [
                "${workspaceFolder}/debug.cfg",   // OpenOCD config file
            ],
            "postRestartCommands": [
              "monitor reset halt",
              "directory ${workspaceFolder}/original_source/rtx"
            ],
            // "preLaunchTask": "Start Debug Server",
            // "postDebugTask": "Stop Debug Server",
            "gdbTarget": "localhost:2331",                     // GDB server port
            "rtos": "auto",
            "swoConfig": {
                "enabled": true,
                "source": "socket",
                "swoPort": "2332",
                "swoFrequency": 4000000,
                "cpuFrequency": 4000000,
                "decoderBytes": 2,
                "decoders": [
                    {
                        "type": "console",
                        "label": "SWO",
                        "port": 0,
                        "filter": "",
                        "showTimestamp": true,
                        "showTimestampInLocalTime": false
                    }
                ]
            }
        },
    ],
    "inputs": [
        {
            "id": "elfChoice",
            "type": "pickString",
            "description": "Select ELF file",
            "options": [
                "${workspaceFolder}/test/images/timesource_test.elf",
                "${workspaceFolder}/test/images/gps_serial_test.elf",
                "${workspaceFolder}/images/pelagic_vms.elf",
            ]
        }
    ]
}