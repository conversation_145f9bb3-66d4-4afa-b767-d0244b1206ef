# Time Functions Test Suite

This test suite verifies which time-related functions in the Pelagic L431 project are actually working and returning incrementing values.

## Purpose

The test was created to identify timing system issues, specifically:
- Functions that always return zero (like HAL_GetTick when uwTick never increments)
- SysTick configuration problems
- Timer interrupt issues
- Non-functioning time counters

## What It Tests

### Core Time Functions
- `HAL_GetTick()` - HAL millisecond tick counter
- `us_ticker_read()` - Microsecond ticker
- `rt_time_get()` - RTX system time
- `rtc_read()` - RTC seconds since epoch
- `clock_read()` - Software epoch clock

### System Components
- SysTick configuration and counting
- Timer interrupt functionality (HAL and RTX)
- Global time variables (`os_time`, `rtc_clock`, `epoch_clock`, `uwTick`)
- Wait function accuracy

### Specific Issue Detection
- **HAL_GetTick stuck at zero**: Detects when `uwTick` never increments
- **SysTick not running**: Checks if <PERSON>ys<PERSON><PERSON> is configured and counting
- **Missing interrupts**: Verifies timer interrupts are firing
- **Broken timing**: Identifies which timing sources are non-functional

## Building and Running

### Build the Test
```bash
cd tests
./run_test.sh
```

Or manually:
```bash
cd tests
export DEVICE_VERSION=10
make fresh
```

### Flash and Run
1. Flash `../images/pelagic_time_test.bin` to your STM32L431 board
2. Connect to serial console (115200 baud)
3. Reset the board to start the test

## Test Output

The test produces detailed output showing:

### Per-Function Results
```
Testing HAL_GetTick (checking for uwTick increment issue)...
  HAL_GetTick sample 0: 0 (uwTick: 0)
  HAL_GetTick sample 1: 0 (uwTick: 0)
  ...
  *** DETECTED: HAL_GetTick stuck at zero - uwTick not incrementing! ***
  Result: FAILED
```

### Summary Report
```
=== TIME FUNCTIONS TEST SUMMARY ===
Function             Status     Initial      Final        Max Inc      Notes
--------             ------     -------      -----        -------      -----
SysTick              FAILED     12345        12345        0            SysTick not working
HAL_GetTick          FAILED     0            0            0            uwTick stuck - SysTick broken
us_ticker_read       WORKING    1000         15000        14000        Should increment in microseconds
...

Summary: 3/8 functions are working correctly
WARNING: Some time functions are not working correctly.
```

## Expected Results

### If Timing is Working
- Most functions should show "WORKING" status
- Values should increment between samples
- SysTick should be enabled and counting
- Timer interrupts should be firing

### If Timing is Broken (like HAL_GetTick issue)
- HAL_GetTick will show "FAILED" with all zeros
- uwTick will never increment
- SysTick may show as not configured or not counting
- Timer interrupts may not be firing

## Continuous Testing

The test runs continuously, repeating every ~30 seconds to catch intermittent issues. This helps identify:
- Timing that works initially but fails later
- Interrupt issues that develop over time
- Inconsistent behavior

## Files

- `time_functions_test.c` - Main test implementation
- `time_functions_test.h` - Test header
- `test_main.c` - Test program entry point
- `Makefile` - Build configuration
- `run_test.sh` - Build and run script
- `README.md` - This documentation

## Integration

The test can be integrated into your development workflow:
1. Run after any timing-related changes
2. Use to verify fixes for timing issues
3. Include in automated testing if desired

## Troubleshooting

### Build Issues
- Ensure `DEVICE_VERSION=10` is set
- Check that all source files are available
- Verify toolchain is properly configured

### Runtime Issues
- Ensure board is properly flashed
- Check serial console connection (115200 baud)
- Verify board reset after flashing

### Test Failures
- Review detailed output to identify specific failing functions
- Check SysTick configuration if HAL_GetTick fails
- Verify timer initialization if us_ticker_read fails
- Look for interrupt configuration issues if timer interrupts fail
