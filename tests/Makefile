# Makefile for time functions test
# This creates a standalone test program to verify timing functions

PROJECT = time_test
TARGET = STM32L431xC
HARDWARE = STM32L431xC
BOARD = STM32L431xC

# Use a recent timestamp for build
BUILDSTAMP := $(shell date +%s)
RELEASE := test

# Override BOATOS to point to original_source since that's where the structure is
BOATOS := $(abspath ../original_source)
TESTDIR := $(abspath .)

# Include common makefile settings
include $(BOATOS)/Makefile.common

# # naturally it got wiped by the include
# BOATOS := $(abspath ../original_source)

# Change the linker script path - the preprocessed version should be created in the same directory
LINKER_SCRIPT_ORIG = $(BOATOS)/cpu/stm32l431xc.ld
LINKER_SCRIPT = $(BOATOS)/cpu/stm32l431xc_pp.ld

# Test-specific source files
TEST_SRC = time_functions_test.c \
           test_main.c

# Override SRC to include only what we need for the test
SRC = start.c \
      board_fault.c \
      bits.c \
      printf.c \
      libc.c \
      chip_uid.c \
      alarm.c \
      rtc_api.c \
      semihost_api.c \
      us_ticker.c \
      wait_api.c \
      mcu_sleep.c \
      os_idle.c \
      datetime.c \
      console.c \
      event.c \
      system_file.c \
      uid.c \
      crc16.c \
      announce.c \
      analogin_api.c \
      analogin_device.c \
      dma.c \
      gpio.c \
      gpio_api.c \
      gpio_irq_api.c \
      gpio_irq_device.c \
      PeripheralPins.c \
      pinmap.c \
      mbed_pinmap_common.c \
      mbed_us_ticker_api.c \
      serial_api.c \
      serial_device.c \
      i2c_api.c \
      port_stubs.c \
      serial_api_stubs.c \
      serial_wire_debug.c \
      stm32l4xx_hal.c \
      stm32l4xx_hal_cortex.c \
      stm32l4xx_hal_pwr.c \
      stm32l4xx_hal_pwr_ex.c \
      stm32l4xx_hal_rtc.c \
      stm32l4xx_hal_rtc_ex.c \
      stm32l4xx_hal_rcc.c \
      stm32l4xx_hal_rcc_ex.c \
      stm32l4xx_hal_uart.c \
      stm32l4xx_hal_uart_ex.c \
      stm32l4xx_hal_i2c.c \
      stm32l4xx_hal_i2c_ex.c \
      stm32l4xx_hal_adc.c \
      stm32l4xx_hal_adc_ex.c \
      hal_tick_overrides.c \
      $(TEST_SRC)

# Override include paths to use correct BOATOS and add test directory
INCLUDE_PATHS = -I. \
        -I$(BOATOS)/common \
        -I$(BOATOS)/rtx \
        -I$(BOATOS)/cpu \
        -I$(BOATOS)/devices \
        -I$(BOATOS)/devices/hal \
        -I$(BOATOS)/devices/platform \
        -I$(BOATOS)/shell \
        -I../tests

# Update VPATH to use correct BOATOS
VPATH=$(BOATOS)/rtx:$(BOATOS)/cpu:$(BOATOS)/devices:$(BOATOS)/shell:$(BOATOS)/common:$(BOATOS)/bnet:$(BOATOS)/$(PROJECT):$(BOATOS)/tests

# Add test-specific defines
BUILD_FLAGS += -DTIME_FUNCTIONS_TEST

# Override the paths to use the correct BOATOS location
OBJDIR = $(BOATOS)/objs/$(PROJECT_TARGET)
IMAGEDIR = $(BOATOS)/images
IMAGEBASE = $(IMAGEDIR)/$(PROJECT_TARGET)

# Create directories
$(OBJDIR) $(IMAGEDIR):
	mkdir -p $@

$(LINKER_SCRIPT):
	$(AD)$(CC) -E -P -x c $(LINKER_SCRIPT_ORIG) -o $(LINKER_SCRIPT)

.PHONY: test clean flash

test: build
	@echo "Time functions test built successfully"
	@echo "Flash the $(IMAGEBASE).bin file to run the test"

clean:
	rm -f $(IMAGEBASE)*{.bin,.elf,.map}
	rm -rf $(OBJDIR)

flash: $(IMAGEBASE).bin
	sh $(BOATOS)/segger-flash.sh $<

fresh: clean build
