/**
 * @file test_main.c
 * @brief Main entry point for time functions test
 */

#include "pelagic.h"
#include "console.h"
#include "system_file.h"
#include "time_functions_test.h"
#include "semihost_api.h"

// Test configuration
#define TEST_REPEAT_INTERVAL_SEC 30

int main(void) {
    // Initialize system
    sys_file_init();
    
    // Initialize console for output
    console_init();
    
    printf("\n");
    printf("========================================\n");
    printf("  PELAGIC TIME FUNCTIONS TEST SUITE\n");
    printf("========================================\n");
    printf("Build: %s\n", __DATE__ " " __TIME__);
    printf("Target: STM32L431xC\n");
    printf("\n");
    
    // Run the test once immediately
    printf("Running initial test...\n");
    int result = run_time_functions_test();
    
    if (result == 0) {
        printf("\n*** TEST PASSED: At least one time function is working ***\n");
    } else {
        printf("\n*** TEST FAILED: No time functions are working! ***\n");
        printf("This indicates a serious timing system problem.\n");
    }
    
    printf("\nTest will repeat every %d seconds...\n", TEST_REPEAT_INTERVAL_SEC);
    printf("Press reset to restart or reflash to exit.\n");
    printf("========================================\n\n");
    
    // Continuous testing loop
    int test_iteration = 1;
    while (1) {
        // Wait for next test interval
        // Use a simple delay loop since we can't trust the timing functions
        for (volatile int i = 0; i < 10000000; i++) {
            // Simple delay loop - approximately 30 seconds on STM32L431
            // This is crude but doesn't rely on any timing functions
        }
        
        printf("\n--- Test Iteration %d ---\n", ++test_iteration);
        result = run_time_functions_test();
        
        if (result == 0) {
            printf("*** ITERATION %d: PASSED ***\n", test_iteration);
        } else {
            printf("*** ITERATION %d: FAILED ***\n", test_iteration);
        }
    }
    
    return 0;
}
