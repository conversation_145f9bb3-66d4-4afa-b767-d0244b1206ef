#!/bin/bash

# <PERSON>ript to build and run the time functions test

echo "=== Building Time Functions Test ==="

# Set device version for build
export DEVICE_VERSION=10

# Clean and build
make clean
make fresh

if [ $? -eq 0 ]; then
    echo ""
    echo "=== Build Successful ==="
    echo "Test binary: ../images/pelagic_time_test.bin"
    echo ""
    echo "To run the test:"
    echo "1. Flash the binary to your STM32L431 board"
    echo "2. Connect to serial console (115200 baud)"
    echo "3. Reset the board to start the test"
    echo ""
    echo "The test will:"
    echo "- Check all time functions for proper incrementing"
    echo "- Specifically detect if HAL_GetTick is stuck at zero"
    echo "- Test SysTick configuration and interrupts"
    echo "- Verify timer interrupt functionality"
    echo "- Report which functions are working vs broken"
    echo ""
    echo "Expected failures if timing is broken:"
    echo "- HAL_GetTick always returns 0"
    echo "- uwTick never increments"
    echo "- SysTick not configured or not counting"
    echo "- Timer interrupts not firing"
    echo ""
else
    echo ""
    echo "=== Build Failed ==="
    echo "Check the error messages above"
    exit 1
fi
