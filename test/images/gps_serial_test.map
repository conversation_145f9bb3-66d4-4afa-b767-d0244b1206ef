Archive member included to satisfy reference by file (symbol)

/usr/lib/gcc/arm-none-eabi/13.2.1/thumb/v7e-m+fp/hard/libgcc.a(_arm_muldf3.o)
                              objs/libc.o (__aeabi_dmul)
/usr/lib/gcc/arm-none-eabi/13.2.1/thumb/v7e-m+fp/hard/libgcc.a(_arm_addsubdf3.o)
                              objs/nmea.o (__aeabi_dsub)
/usr/lib/gcc/arm-none-eabi/13.2.1/thumb/v7e-m+fp/hard/libgcc.a(_arm_muldivdf3.o)
                              objs/libc.o (__aeabi_ddiv)
/usr/lib/gcc/arm-none-eabi/13.2.1/thumb/v7e-m+fp/hard/libgcc.a(_arm_fixdfsi.o)
                              objs/datetime.o (__aeabi_d2iz)
/usr/lib/gcc/arm-none-eabi/13.2.1/thumb/v7e-m+fp/hard/libgcc.a(_arm_truncdfsf2.o)
                              objs/nmea.o (__aeabi_d2f)
/usr/lib/gcc/arm-none-eabi/13.2.1/thumb/v7e-m+fp/hard/libgcc.a(_aeabi_uldivmod.o)
                              objs/rt_CMSIS.o (__aeabi_uldivmod)
/usr/lib/gcc/arm-none-eabi/13.2.1/thumb/v7e-m+fp/hard/libgcc.a(_udivmoddi4.o)
                              /usr/lib/gcc/arm-none-eabi/13.2.1/thumb/v7e-m+fp/hard/libgcc.a(_aeabi_uldivmod.o) (__udivmoddi4)
/usr/lib/gcc/arm-none-eabi/13.2.1/thumb/v7e-m+fp/hard/libgcc.a(_dvmd_tls.o)
                              /usr/lib/gcc/arm-none-eabi/13.2.1/thumb/v7e-m+fp/hard/libgcc.a(_aeabi_uldivmod.o) (__aeabi_ldiv0)
/usr/lib/gcc/arm-none-eabi/13.2.1/../../../arm-none-eabi/lib/thumb/v7e-m+fp/hard/libm.a(libm_a-sf_trunc.o)
                              objs/nmea.o (truncf)

Discarded input sections

 .text          0x00000000       0x14 objs/startup_stm32l431xx.o
 .data          0x00000000        0x0 objs/startup_stm32l431xx.o
 .bss           0x00000000        0x0 objs/startup_stm32l431xx.o
 .text          0x00000000        0x0 objs/SVC_Table.o
 .data          0x00000000        0x0 objs/SVC_Table.o
 .bss           0x00000000        0x0 objs/SVC_Table.o
 .ARM.attributes
                0x00000000       0x20 objs/SVC_Table.o
 .data          0x00000000        0x0 objs/HAL_CM4.o
 .bss           0x00000000        0x0 objs/HAL_CM4.o
 .ARM.extab     0x00000000        0x0 objs/HAL_CM4.o
 .text          0x00000000        0x0 objs/HAL_CM.o
 .data          0x00000000        0x0 objs/HAL_CM.o
 .bss           0x00000000        0x0 objs/HAL_CM.o
 .text          0x00000000        0x0 objs/RTX_Conf_CM.o
 .data          0x00000000        0x0 objs/RTX_Conf_CM.o
 .bss           0x00000000        0x0 objs/RTX_Conf_CM.o
 .rodata.os_tickfreq
                0x00000000        0x4 objs/RTX_Conf_CM.o
 .rodata.os_tickus_i
                0x00000000        0x2 objs/RTX_Conf_CM.o
 .rodata.os_tickus_f
                0x00000000        0x2 objs/RTX_Conf_CM.o
 .rodata.CMSIS_RTOS_API_Version
                0x00000000        0x4 objs/RTX_Conf_CM.o
 .rodata.CMSIS_RTOS_RTX_Version
                0x00000000        0x4 objs/RTX_Conf_CM.o
 .rodata.os_timernum
                0x00000000        0x4 objs/RTX_Conf_CM.o
 .bss.os_tmr    0x00000000        0x4 objs/RTX_Conf_CM.o
 .text.software_init_hook
                0x00000000       0x34 objs/RTX_Conf_CM.o
 .text          0x00000000        0x0 objs/rt_CMSIS.o
 .data          0x00000000        0x0 objs/rt_CMSIS.o
 .bss           0x00000000        0x0 objs/rt_CMSIS.o
 .text.svcKernelRunning
                0x00000000       0x18 objs/rt_CMSIS.o
 .text.svcKernelSysTick
                0x00000000       0x68 objs/rt_CMSIS.o
 .text.osKernelRunning
                0x00000000       0x44 objs/rt_CMSIS.o
 .text.osKernelSysTick
                0x00000000       0x2c objs/rt_CMSIS.o
 .text.svcThreadYield
                0x00000000        0xe objs/rt_CMSIS.o
 .text.svcThreadSetPriority
                0x00000000       0x60 objs/rt_CMSIS.o
 .text.svcThreadGetPriority
                0x00000000       0x2c objs/rt_CMSIS.o
 .text.osThreadTerminate
                0x00000000       0x34 objs/rt_CMSIS.o
 .text.osThreadYield
                0x00000000       0x2c objs/rt_CMSIS.o
 .text.osThreadSetPriority
                0x00000000       0x3c objs/rt_CMSIS.o
 .text.osThreadGetPriority
                0x00000000       0x38 objs/rt_CMSIS.o
 .text.osWait   0x00000000       0x28 objs/rt_CMSIS.o
 .text.rt_timer_remove
                0x00000000       0x80 objs/rt_CMSIS.o
 .text.svcTimerCreate
                0x00000000       0xa4 objs/rt_CMSIS.o
 .text.svcTimerStart
                0x00000000      0x11c objs/rt_CMSIS.o
 .text.svcTimerStop
                0x00000000       0x46 objs/rt_CMSIS.o
 .text.svcTimerDelete
                0x00000000       0x44 objs/rt_CMSIS.o
 .text.sysUserTimerWakeupTime
                0x00000000       0x28 objs/rt_CMSIS.o
 .text.sysUserTimerUpdate
                0x00000000       0x60 objs/rt_CMSIS.o
 .text.osTimerCreate
                0x00000000       0x74 objs/rt_CMSIS.o
 .text.osTimerStart
                0x00000000       0x3c objs/rt_CMSIS.o
 .text.osTimerStop
                0x00000000       0x34 objs/rt_CMSIS.o
 .text.osTimerDelete
                0x00000000       0x34 objs/rt_CMSIS.o
 .text.svcSignalSet
                0x00000000       0x4c objs/rt_CMSIS.o
 .text.svcSignalClear
                0x00000000       0x4c objs/rt_CMSIS.o
 .text.isrSignalSet
                0x00000000       0x4c objs/rt_CMSIS.o
 .text.osSignalSet
                0x00000000       0x44 objs/rt_CMSIS.o
 .text.osSignalClear
                0x00000000       0x40 objs/rt_CMSIS.o
 .text.svcMutexCreate
                0x00000000       0x50 objs/rt_CMSIS.o
 .text.svcMutexDelete
                0x00000000       0x36 objs/rt_CMSIS.o
 .text.osMutexCreate
                0x00000000       0x5c objs/rt_CMSIS.o
 .text.osMutexDelete
                0x00000000       0x34 objs/rt_CMSIS.o
 .text.svcSemaphoreCreate
                0x00000000       0x6a objs/rt_CMSIS.o
 .text.svcSemaphoreWait
                0x00000000       0x58 objs/rt_CMSIS.o
 .text.svcSemaphoreRelease
                0x00000000       0x46 objs/rt_CMSIS.o
 .text.svcSemaphoreDelete
                0x00000000       0x36 objs/rt_CMSIS.o
 .text.isrSemaphoreRelease
                0x00000000       0x46 objs/rt_CMSIS.o
 .text.osSemaphoreCreate
                0x00000000       0x64 objs/rt_CMSIS.o
 .text.osSemaphoreWait
                0x00000000       0x40 objs/rt_CMSIS.o
 .text.osSemaphoreRelease
                0x00000000       0x3c objs/rt_CMSIS.o
 .text.osSemaphoreDelete
                0x00000000       0x34 objs/rt_CMSIS.o
 .text.rt_clr_box
                0x00000000       0x3e objs/rt_CMSIS.o
 .text.svcPoolCreate
                0x00000000       0x60 objs/rt_CMSIS.o
 .text.sysPoolAlloc
                0x00000000       0x34 objs/rt_CMSIS.o
 .text.sysPoolFree
                0x00000000       0x32 objs/rt_CMSIS.o
 .text.osPoolCreate
                0x00000000       0x5c objs/rt_CMSIS.o
 .text.osPoolAlloc
                0x00000000       0x54 objs/rt_CMSIS.o
 .text.osPoolCAlloc
                0x00000000       0x54 objs/rt_CMSIS.o
 .text.osPoolFree
                0x00000000       0x54 objs/rt_CMSIS.o
 .text.svcMessagePut
                0x00000000       0x58 objs/rt_CMSIS.o
 .text.osMessageCreate
                0x00000000       0x64 objs/rt_CMSIS.o
 .text.osMessagePut
                0x00000000       0x50 objs/rt_CMSIS.o
 .text.svcMailCreate
                0x00000000       0xa2 objs/rt_CMSIS.o
 .text.sysMailAlloc
                0x00000000       0xbc objs/rt_CMSIS.o
 .text.sysMailFree
                0x00000000       0xa2 objs/rt_CMSIS.o
 .text.osMailCreate
                0x00000000       0x64 objs/rt_CMSIS.o
 .text.osMailAlloc
                0x00000000       0x54 objs/rt_CMSIS.o
 .text.osMailCAlloc
                0x00000000       0x54 objs/rt_CMSIS.o
 .text.osMailFree
                0x00000000       0x4c objs/rt_CMSIS.o
 .text.osMailPut
                0x00000000       0x36 objs/rt_CMSIS.o
 .text.osMailGet
                0x00000000       0x5a objs/rt_CMSIS.o
 .text.os_suspend
                0x00000000       0x14 objs/rt_CMSIS.o
 .text.os_resume
                0x00000000       0x20 objs/rt_CMSIS.o
 .text          0x00000000        0x0 objs/rt_Event.o
 .data          0x00000000        0x0 objs/rt_Event.o
 .bss           0x00000000        0x0 objs/rt_Event.o
 .text.rt_evt_set
                0x00000000       0xbc objs/rt_Event.o
 .text.rt_evt_clr
                0x00000000       0x48 objs/rt_Event.o
 .text.isr_evt_set
                0x00000000       0x3c objs/rt_Event.o
 .text.rt_evt_get
                0x00000000       0x18 objs/rt_Event.o
 .text          0x00000000        0x0 objs/rt_List.o
 .data          0x00000000        0x0 objs/rt_List.o
 .bss           0x00000000        0x0 objs/rt_List.o
 .text.rt_get_same_rdy_prio
                0x00000000       0x40 objs/rt_List.o
 .text          0x00000000        0x0 objs/rt_Mailbox.o
 .data          0x00000000        0x0 objs/rt_Mailbox.o
 .bss           0x00000000        0x0 objs/rt_Mailbox.o
 .text.rt_mbx_send
                0x00000000       0xf8 objs/rt_Mailbox.o
 .text          0x00000000        0x0 objs/rt_MemBox.o
 .data          0x00000000        0x0 objs/rt_MemBox.o
 .bss           0x00000000        0x0 objs/rt_MemBox.o
 .text._calloc_box
                0x00000000       0x44 objs/rt_MemBox.o
 .text          0x00000000        0x0 objs/rt_Memory.o
 .data          0x00000000        0x0 objs/rt_Memory.o
 .bss           0x00000000        0x0 objs/rt_Memory.o
 .text          0x00000000        0x0 objs/rt_Mutex.o
 .data          0x00000000        0x0 objs/rt_Mutex.o
 .bss           0x00000000        0x0 objs/rt_Mutex.o
 .text.rt_mut_init
                0x00000000       0x36 objs/rt_Mutex.o
 .text.rt_mut_delete
                0x00000000      0x124 objs/rt_Mutex.o
 .text          0x00000000        0x0 objs/rt_Robin.o
 .data          0x00000000        0x0 objs/rt_Robin.o
 .bss           0x00000000        0x0 objs/rt_Robin.o
 .text          0x00000000        0x0 objs/rt_Semaphore.o
 .data          0x00000000        0x0 objs/rt_Semaphore.o
 .bss           0x00000000        0x0 objs/rt_Semaphore.o
 .text.rt_sem_init
                0x00000000       0x2e objs/rt_Semaphore.o
 .text.rt_sem_delete
                0x00000000       0x84 objs/rt_Semaphore.o
 .text.rt_sem_send
                0x00000000       0x48 objs/rt_Semaphore.o
 .text.rt_sem_wait
                0x00000000       0x78 objs/rt_Semaphore.o
 .text.isr_sem_send
                0x00000000       0x20 objs/rt_Semaphore.o
 .text          0x00000000        0x0 objs/rt_System.o
 .data          0x00000000        0x0 objs/rt_System.o
 .bss           0x00000000        0x0 objs/rt_System.o
 .text.rt_systick_val
                0x00000000       0x20 objs/rt_System.o
 .text.rt_systick_ovf
                0x00000000       0x1c objs/rt_System.o
 .bss.pend_flags
                0x00000000        0x1 objs/rt_System.o
 .text.rt_suspend
                0x00000000       0x40 objs/rt_System.o
 .text.rt_resume
                0x00000000       0xe4 objs/rt_System.o
 .text.rt_tsk_lock
                0x00000000       0x98 objs/rt_System.o
 .text.rt_tsk_unlock
                0x00000000       0x98 objs/rt_System.o
 .text.os_tick_val
                0x00000000        0xe objs/rt_System.o
 .text.os_tick_ovf
                0x00000000        0xe objs/rt_System.o
 .text          0x00000000        0x0 objs/rt_Task.o
 .data          0x00000000        0x0 objs/rt_Task.o
 .bss           0x00000000        0x0 objs/rt_Task.o
 .text.rt_tsk_pass
                0x00000000       0x3c objs/rt_Task.o
 .text          0x00000000        0x0 objs/rt_Time.o
 .data          0x00000000        0x0 objs/rt_Time.o
 .bss           0x00000000        0x0 objs/rt_Time.o
 .text.rt_time_get
                0x00000000       0x18 objs/rt_Time.o
 .text.rt_itv_set
                0x00000000       0x38 objs/rt_Time.o
 .text.rt_itv_wait
                0x00000000       0x50 objs/rt_Time.o
 .text          0x00000000        0x0 objs/system_stm32l4xx.o
 .data          0x00000000        0x0 objs/system_stm32l4xx.o
 .bss           0x00000000        0x0 objs/system_stm32l4xx.o
 .rodata.AHBPrescTable
                0x00000000       0x10 objs/system_stm32l4xx.o
 .text.SystemCoreClockUpdate
                0x00000000      0x15c objs/system_stm32l4xx.o
 .text          0x00000000        0x0 objs/sleep.o
 .data          0x00000000        0x0 objs/sleep.o
 .bss           0x00000000        0x0 objs/sleep.o
 .debug_line    0x00000000        0x0 objs/sleep.o
 .debug_str     0x00000000      0x120 objs/sleep.o
 .comment       0x00000000       0x27 objs/sleep.o
 .ARM.attributes
                0x00000000       0x34 objs/sleep.o
 .text          0x00000000        0x0 objs/start.o
 .data          0x00000000        0x0 objs/start.o
 .bss           0x00000000        0x0 objs/start.o
 .rodata.OFFSET_TAB_CCMRx
                0x00000000        0x9 objs/start.o
 .rodata.SHIFT_TAB_OCxx
                0x00000000        0x9 objs/start.o
 .rodata.SHIFT_TAB_ICxx
                0x00000000        0x9 objs/start.o
 .rodata.SHIFT_TAB_CCxP
                0x00000000        0x9 objs/start.o
 .rodata.SHIFT_TAB_OISx
                0x00000000        0x9 objs/start.o
 .text          0x00000000        0x0 objs/board_fault.o
 .data          0x00000000        0x0 objs/board_fault.o
 .bss           0x00000000        0x0 objs/board_fault.o
 .rodata.OFFSET_TAB_CCMRx
                0x00000000        0x9 objs/board_fault.o
 .rodata.SHIFT_TAB_OCxx
                0x00000000        0x9 objs/board_fault.o
 .rodata.SHIFT_TAB_ICxx
                0x00000000        0x9 objs/board_fault.o
 .rodata.SHIFT_TAB_CCxP
                0x00000000        0x9 objs/board_fault.o
 .rodata.SHIFT_TAB_OISx
                0x00000000        0x9 objs/board_fault.o
 .text.board_fault_check
                0x00000000       0xb4 objs/board_fault.o
 .text          0x00000000        0x0 objs/bits.o
 .data          0x00000000        0x0 objs/bits.o
 .bss           0x00000000        0x0 objs/bits.o
 .text.get_uint16
                0x00000000       0x2c objs/bits.o
 .text.set_uint16
                0x00000000       0x2e objs/bits.o
 .text.get_uint32
                0x00000000       0x38 objs/bits.o
 .text.set_uint32
                0x00000000       0x42 objs/bits.o
 .text.get_uint64
                0x00000000      0x16a objs/bits.o
 .text.set_uint64
                0x00000000       0xd2 objs/bits.o
 .text          0x00000000        0x0 objs/printf.o
 .data          0x00000000        0x0 objs/printf.o
 .bss           0x00000000        0x0 objs/printf.o
 .rodata.OFFSET_TAB_CCMRx
                0x00000000        0x9 objs/printf.o
 .rodata.SHIFT_TAB_OCxx
                0x00000000        0x9 objs/printf.o
 .rodata.SHIFT_TAB_ICxx
                0x00000000        0x9 objs/printf.o
 .rodata.SHIFT_TAB_CCxP
                0x00000000        0x9 objs/printf.o
 .rodata.SHIFT_TAB_OISx
                0x00000000        0x9 objs/printf.o
 .bss.os_mutex_cb_printf_mutex
                0x00000000       0x10 objs/printf.o
 .rodata.os_mutex_def_printf_mutex
                0x00000000        0x4 objs/printf.o
 .text.printf_init
                0x00000000       0x1c objs/printf.o
 .text.puts     0x00000000       0x1c objs/printf.o
 .text.hex_dump
                0x00000000       0x34 objs/printf.o
 .text          0x00000000        0x0 objs/libc.o
 .data          0x00000000        0x0 objs/libc.o
 .bss           0x00000000        0x0 objs/libc.o
 .rodata.OFFSET_TAB_CCMRx
                0x00000000        0x9 objs/libc.o
 .rodata.SHIFT_TAB_OCxx
                0x00000000        0x9 objs/libc.o
 .rodata.SHIFT_TAB_ICxx
                0x00000000        0x9 objs/libc.o
 .rodata.SHIFT_TAB_CCxP
                0x00000000        0x9 objs/libc.o
 .rodata.SHIFT_TAB_OISx
                0x00000000        0x9 objs/libc.o
 .text.__errno  0x00000000       0x10 objs/libc.o
 .text.strcpy   0x00000000       0x30 objs/libc.o
 .text.abs      0x00000000       0x1c objs/libc.o
 .data.random_seed
                0x00000000        0x4 objs/libc.o
 .text.random_setup_seed
                0x00000000       0x4c objs/libc.o
 .text.rand     0x00000000       0x38 objs/libc.o
 .text          0x00000000        0x0 objs/chip_uid.o
 .data          0x00000000        0x0 objs/chip_uid.o
 .bss           0x00000000        0x0 objs/chip_uid.o
 .text.chip_uid
                0x00000000       0xa0 objs/chip_uid.o
 .debug_info    0x00000000       0xcb objs/chip_uid.o
 .debug_abbrev  0x00000000       0x80 objs/chip_uid.o
 .debug_aranges
                0x00000000       0x20 objs/chip_uid.o
 .debug_rnglists
                0x00000000       0x14 objs/chip_uid.o
 .debug_line    0x00000000       0xe9 objs/chip_uid.o
 .debug_str     0x00000000      0x1cf objs/chip_uid.o
 .comment       0x00000000       0x27 objs/chip_uid.o
 .debug_frame   0x00000000       0x38 objs/chip_uid.o
 .ARM.attributes
                0x00000000       0x34 objs/chip_uid.o
 .text          0x00000000        0x0 objs/alarm.o
 .data          0x00000000        0x0 objs/alarm.o
 .bss           0x00000000        0x0 objs/alarm.o
 .rodata.OFFSET_TAB_CCMRx
                0x00000000        0x9 objs/alarm.o
 .rodata.SHIFT_TAB_OCxx
                0x00000000        0x9 objs/alarm.o
 .rodata.SHIFT_TAB_ICxx
                0x00000000        0x9 objs/alarm.o
 .rodata.SHIFT_TAB_CCxP
                0x00000000        0x9 objs/alarm.o
 .rodata.SHIFT_TAB_OISx
                0x00000000        0x9 objs/alarm.o
 .bss.alarm_head
                0x00000000        0x4 objs/alarm.o
 .text.alarm_tick
                0x00000000       0x78 objs/alarm.o
 .text.alarm_remain
                0x00000000       0x38 objs/alarm.o
 .text.alarm_start_common
                0x00000000       0x7c objs/alarm.o
 .text.alarm_cancel
                0x00000000       0x5c objs/alarm.o
 .text.alarm_start_periodic
                0x00000000       0x30 objs/alarm.o
 .text.alarm_start_set
                0x00000000       0x38 objs/alarm.o
 .text.alarm_start
                0x00000000       0x30 objs/alarm.o
 .debug_info    0x00000000      0x857 objs/alarm.o
 .debug_abbrev  0x00000000      0x1fd objs/alarm.o
 .debug_aranges
                0x00000000       0x50 objs/alarm.o
 .debug_rnglists
                0x00000000       0x43 objs/alarm.o
 .debug_line    0x00000000      0x353 objs/alarm.o
 .debug_str     0x00000000      0xb04 objs/alarm.o
 .comment       0x00000000       0x27 objs/alarm.o
 .debug_frame   0x00000000      0x118 objs/alarm.o
 .ARM.attributes
                0x00000000       0x34 objs/alarm.o
 .text          0x00000000        0x0 objs/rtc_api.o
 .data          0x00000000        0x0 objs/rtc_api.o
 .bss           0x00000000        0x0 objs/rtc_api.o
 .rodata.OFFSET_TAB_CCMRx
                0x00000000        0x9 objs/rtc_api.o
 .rodata.SHIFT_TAB_OCxx
                0x00000000        0x9 objs/rtc_api.o
 .rodata.SHIFT_TAB_ICxx
                0x00000000        0x9 objs/rtc_api.o
 .rodata.SHIFT_TAB_CCxP
                0x00000000        0x9 objs/rtc_api.o
 .rodata.SHIFT_TAB_OISx
                0x00000000        0x9 objs/rtc_api.o
 .text.LL_RTC_IsActiveFlag_INITS
                0x00000000       0x26 objs/rtc_api.o
 .bss.RTC_inited
                0x00000000        0x4 objs/rtc_api.o
 .bss.RtcHandle
                0x00000000       0x24 objs/rtc_api.o
 .rodata        0x00000000       0xb3 objs/rtc_api.o
 .text.rtc_init
                0x00000000      0x154 objs/rtc_api.o
 .text.rtc_free
                0x00000000        0xe objs/rtc_api.o
 .text.rtc_write
                0x00000000       0xe4 objs/rtc_api.o
 .text.rtc_isenabled
                0x00000000       0x14 objs/rtc_api.o
 .text          0x00000000        0x0 objs/semihost_api.o
 .data          0x00000000        0x0 objs/semihost_api.o
 .bss           0x00000000        0x0 objs/semihost_api.o
 .rodata.OFFSET_TAB_CCMRx
                0x00000000        0x9 objs/semihost_api.o
 .rodata.SHIFT_TAB_OCxx
                0x00000000        0x9 objs/semihost_api.o
 .rodata.SHIFT_TAB_ICxx
                0x00000000        0x9 objs/semihost_api.o
 .rodata.SHIFT_TAB_CCxP
                0x00000000        0x9 objs/semihost_api.o
 .rodata.SHIFT_TAB_OISx
                0x00000000        0x9 objs/semihost_api.o
 .text.__semihost
                0x00000000       0x22 objs/semihost_api.o
 .text.semihost_open
                0x00000000       0x32 objs/semihost_api.o
 .text.semihost_close
                0x00000000       0x1c objs/semihost_api.o
 .text.semihost_write
                0x00000000       0x38 objs/semihost_api.o
 .text.semihost_read
                0x00000000       0x2e objs/semihost_api.o
 .text.semihost_istty
                0x00000000       0x1c objs/semihost_api.o
 .text.semihost_seek
                0x00000000       0x28 objs/semihost_api.o
 .text.semihost_ensure
                0x00000000       0x1c objs/semihost_api.o
 .text.semihost_flen
                0x00000000       0x1c objs/semihost_api.o
 .text.semihost_remove
                0x00000000       0x2c objs/semihost_api.o
 .text.semihost_rename
                0x00000000       0x3c objs/semihost_api.o
 .text.semihost_writec
                0x00000000       0x1a objs/semihost_api.o
 .text.semihost_readc
                0x00000000       0x12 objs/semihost_api.o
 .text.semihost_exit
                0x00000000       0x26 objs/semihost_api.o
 .text.semihost_uid
                0x00000000       0x28 objs/semihost_api.o
 .text.semihost_reset
                0x00000000       0x14 objs/semihost_api.o
 .text.semihost_vbus
                0x00000000       0x14 objs/semihost_api.o
 .text.semihost_powerdown
                0x00000000       0x14 objs/semihost_api.o
 .data.is_debugger_attached
                0x00000000        0x4 objs/semihost_api.o
 .text.semihost_connected
                0x00000000       0x18 objs/semihost_api.o
 .text.semihost_disabledebug
                0x00000000       0x20 objs/semihost_api.o
 .debug_info    0x00000000      0x52f objs/semihost_api.o
 .debug_abbrev  0x00000000      0x15e objs/semihost_api.o
 .debug_aranges
                0x00000000       0xb8 objs/semihost_api.o
 .debug_rnglists
                0x00000000       0x85 objs/semihost_api.o
 .debug_line    0x00000000      0x35b objs/semihost_api.o
 .debug_str     0x00000000      0x3bd objs/semihost_api.o
 .comment       0x00000000       0x27 objs/semihost_api.o
 .debug_frame   0x00000000      0x2b8 objs/semihost_api.o
 .ARM.attributes
                0x00000000       0x34 objs/semihost_api.o
 .text          0x00000000        0x0 objs/us_ticker.o
 .data          0x00000000        0x0 objs/us_ticker.o
 .bss           0x00000000        0x0 objs/us_ticker.o
 .text.__NVIC_EnableIRQ
                0x00000000       0x3c objs/us_ticker.o
 .text.__NVIC_SetVector
                0x00000000       0x34 objs/us_ticker.o
 .rodata.OFFSET_TAB_CCMRx
                0x00000000        0x9 objs/us_ticker.o
 .rodata.SHIFT_TAB_OCxx
                0x00000000        0x9 objs/us_ticker.o
 .rodata.SHIFT_TAB_ICxx
                0x00000000        0x9 objs/us_ticker.o
 .rodata.SHIFT_TAB_CCxP
                0x00000000        0x9 objs/us_ticker.o
 .rodata.SHIFT_TAB_OISx
                0x00000000        0x9 objs/us_ticker.o
 .text.LL_TIM_GenerateEvent_CC1
                0x00000000       0x20 objs/us_ticker.o
 .bss.TimMasterHandle
                0x00000000       0x4c objs/us_ticker.o
 .bss.timer_cnt_reg
                0x00000000        0x4 objs/us_ticker.o
 .bss.timer_ccr1_reg
                0x00000000        0x4 objs/us_ticker.o
 .bss.timer_dier_reg
                0x00000000        0x4 objs/us_ticker.o
 .text.us_ticker_get_info
                0x00000000       0x14 objs/us_ticker.o
 .text.timer_irq_handler
                0x00000000       0x40 objs/us_ticker.o
 .text.init_32bit_timer
                0x00000000       0xf0 objs/us_ticker.o
 .text.us_ticker_init
                0x00000000       0x24 objs/us_ticker.o
 .text.us_ticker_read
                0x00000000       0x14 objs/us_ticker.o
 .text.us_ticker_set_interrupt
                0x00000000       0x3c objs/us_ticker.o
 .text.us_ticker_fire_interrupt
                0x00000000       0x30 objs/us_ticker.o
 .text.us_ticker_disable_interrupt
                0x00000000       0x24 objs/us_ticker.o
 .text.us_ticker_clear_interrupt
                0x00000000       0x1c objs/us_ticker.o
 .text.save_timer_ctx
                0x00000000       0x3c objs/us_ticker.o
 .text.restore_timer_ctx
                0x00000000       0x3c objs/us_ticker.o
 .text.us_ticker_free
                0x00000000       0x18 objs/us_ticker.o
 .rodata.info.0
                0x00000000        0x8 objs/us_ticker.o
 .debug_info    0x00000000     0x1174 objs/us_ticker.o
 .debug_abbrev  0x00000000      0x335 objs/us_ticker.o
 .debug_aranges
                0x00000000       0x90 objs/us_ticker.o
 .debug_rnglists
                0x00000000       0x68 objs/us_ticker.o
 .debug_line    0x00000000      0x36c objs/us_ticker.o
 .debug_str     0x00000000      0xfd1 objs/us_ticker.o
 .comment       0x00000000       0x27 objs/us_ticker.o
 .debug_frame   0x00000000      0x204 objs/us_ticker.o
 .ARM.attributes
                0x00000000       0x34 objs/us_ticker.o
 .text          0x00000000        0x0 objs/wait_api.o
 .data          0x00000000        0x0 objs/wait_api.o
 .bss           0x00000000        0x0 objs/wait_api.o
 .rodata.OFFSET_TAB_CCMRx
                0x00000000        0x9 objs/wait_api.o
 .rodata.SHIFT_TAB_OCxx
                0x00000000        0x9 objs/wait_api.o
 .rodata.SHIFT_TAB_ICxx
                0x00000000        0x9 objs/wait_api.o
 .rodata.SHIFT_TAB_CCxP
                0x00000000        0x9 objs/wait_api.o
 .rodata.SHIFT_TAB_OISx
                0x00000000        0x9 objs/wait_api.o
 .text.wait     0x00000000       0x30 objs/wait_api.o
 .text.wait_ms  0x00000000       0x20 objs/wait_api.o
 .text.wait_us  0x00000000       0x2a objs/wait_api.o
 .debug_info    0x00000000      0x1a2 objs/wait_api.o
 .debug_abbrev  0x00000000       0xc4 objs/wait_api.o
 .debug_aranges
                0x00000000       0x30 objs/wait_api.o
 .debug_rnglists
                0x00000000       0x1f objs/wait_api.o
 .debug_line    0x00000000      0x132 objs/wait_api.o
 .debug_str     0x00000000      0x245 objs/wait_api.o
 .comment       0x00000000       0x27 objs/wait_api.o
 .debug_frame   0x00000000       0x7c objs/wait_api.o
 .ARM.attributes
                0x00000000       0x34 objs/wait_api.o
 .text          0x00000000        0x0 objs/mcu_sleep.o
 .data          0x00000000        0x0 objs/mcu_sleep.o
 .bss           0x00000000        0x0 objs/mcu_sleep.o
 .rodata.OFFSET_TAB_CCMRx
                0x00000000        0x9 objs/mcu_sleep.o
 .rodata.SHIFT_TAB_OCxx
                0x00000000        0x9 objs/mcu_sleep.o
 .rodata.SHIFT_TAB_ICxx
                0x00000000        0x9 objs/mcu_sleep.o
 .rodata.SHIFT_TAB_CCxP
                0x00000000        0x9 objs/mcu_sleep.o
 .rodata.SHIFT_TAB_OISx
                0x00000000        0x9 objs/mcu_sleep.o
 .text.final_power_off
                0x00000000       0x18 objs/mcu_sleep.o
 .text.mcu_sleep
                0x00000000       0x1c objs/mcu_sleep.o
 .debug_info    0x00000000      0x592 objs/mcu_sleep.o
 .debug_abbrev  0x00000000      0x1a7 objs/mcu_sleep.o
 .debug_aranges
                0x00000000       0x28 objs/mcu_sleep.o
 .debug_rnglists
                0x00000000       0x19 objs/mcu_sleep.o
 .debug_line    0x00000000      0x1d4 objs/mcu_sleep.o
 .debug_str     0x00000000      0x5e3 objs/mcu_sleep.o
 .comment       0x00000000       0x27 objs/mcu_sleep.o
 .debug_frame   0x00000000       0x54 objs/mcu_sleep.o
 .ARM.attributes
                0x00000000       0x34 objs/mcu_sleep.o
 .text          0x00000000        0x0 objs/os_idle.o
 .data          0x00000000        0x0 objs/os_idle.o
 .bss           0x00000000        0x0 objs/os_idle.o
 .rodata.OFFSET_TAB_CCMRx
                0x00000000        0x9 objs/os_idle.o
 .rodata.SHIFT_TAB_OCxx
                0x00000000        0x9 objs/os_idle.o
 .rodata.SHIFT_TAB_ICxx
                0x00000000        0x9 objs/os_idle.o
 .rodata.SHIFT_TAB_CCxP
                0x00000000        0x9 objs/os_idle.o
 .rodata.SHIFT_TAB_OISx
                0x00000000        0x9 objs/os_idle.o
 .text          0x00000000        0x0 objs/datetime.o
 .data          0x00000000        0x0 objs/datetime.o
 .bss           0x00000000        0x0 objs/datetime.o
 .rodata.OFFSET_TAB_CCMRx
                0x00000000        0x9 objs/datetime.o
 .rodata.SHIFT_TAB_OCxx
                0x00000000        0x9 objs/datetime.o
 .rodata.SHIFT_TAB_ICxx
                0x00000000        0x9 objs/datetime.o
 .rodata.SHIFT_TAB_CCxP
                0x00000000        0x9 objs/datetime.o
 .rodata.SHIFT_TAB_OISx
                0x00000000        0x9 objs/datetime.o
 .text.timezone_offset
                0x00000000       0x68 objs/datetime.o
 .text          0x00000000        0x0 objs/console.o
 .data          0x00000000        0x0 objs/console.o
 .bss           0x00000000        0x0 objs/console.o
 .rodata.OFFSET_TAB_CCMRx
                0x00000000        0x9 objs/console.o
 .rodata.SHIFT_TAB_OCxx
                0x00000000        0x9 objs/console.o
 .rodata.SHIFT_TAB_ICxx
                0x00000000        0x9 objs/console.o
 .rodata.SHIFT_TAB_CCxP
                0x00000000        0x9 objs/console.o
 .rodata.SHIFT_TAB_OISx
                0x00000000        0x9 objs/console.o
 .text.console_getchar
                0x00000000       0x20 objs/console.o
 .text.console_available
                0x00000000       0x20 objs/console.o
 .text.have_esc
                0x00000000       0x2e objs/console.o
 .rodata        0x00000000       0x27 objs/console.o
 .text.display_more
                0x00000000       0x50 objs/console.o
 .text          0x00000000        0x0 objs/event.o
 .data          0x00000000        0x0 objs/event.o
 .bss           0x00000000        0x0 objs/event.o
 .rodata.OFFSET_TAB_CCMRx
                0x00000000        0x9 objs/event.o
 .rodata.SHIFT_TAB_OCxx
                0x00000000        0x9 objs/event.o
 .rodata.SHIFT_TAB_ICxx
                0x00000000        0x9 objs/event.o
 .rodata.SHIFT_TAB_CCxP
                0x00000000        0x9 objs/event.o
 .rodata.SHIFT_TAB_OISx
                0x00000000        0x9 objs/event.o
 .bss.os_mutex_cb_event_display_mutex
                0x00000000       0x10 objs/event.o
 .rodata.os_mutex_def_event_display_mutex
                0x00000000        0x4 objs/event.o
 .text.event_init
                0x00000000       0x1c objs/event.o
 .text.event_dump
                0x00000000       0x5c objs/event.o
 .text.event_log_reset
                0x00000000       0x18 objs/event.o
 .text.event_log_read
                0x00000000      0x1b0 objs/event.o
 .text.event_settings_init
                0x00000000       0x20 objs/event.o
 .text.event_setting
                0x00000000       0xc4 objs/event.o
 .text.event_setting_dump
                0x00000000       0x48 objs/event.o
 .bss.uid.0     0x00000000        0xa objs/event.o
 .text          0x00000000        0x0 objs/system_file.o
 .data          0x00000000        0x0 objs/system_file.o
 .bss           0x00000000        0x0 objs/system_file.o
 .rodata.OFFSET_TAB_CCMRx
                0x00000000        0x9 objs/system_file.o
 .rodata.SHIFT_TAB_OCxx
                0x00000000        0x9 objs/system_file.o
 .rodata.SHIFT_TAB_ICxx
                0x00000000        0x9 objs/system_file.o
 .rodata.SHIFT_TAB_CCxP
                0x00000000        0x9 objs/system_file.o
 .rodata.SHIFT_TAB_OISx
                0x00000000        0x9 objs/system_file.o
 .text.sys_file_clear
                0x00000000       0x20 objs/system_file.o
 .text          0x00000000        0x0 objs/uid.o
 .data          0x00000000        0x0 objs/uid.o
 .bss           0x00000000        0x0 objs/uid.o
 .rodata.OFFSET_TAB_CCMRx
                0x00000000        0x9 objs/uid.o
 .rodata.SHIFT_TAB_OCxx
                0x00000000        0x9 objs/uid.o
 .rodata.SHIFT_TAB_ICxx
                0x00000000        0x9 objs/uid.o
 .rodata.SHIFT_TAB_CCxP
                0x00000000        0x9 objs/uid.o
 .rodata.SHIFT_TAB_OISx
                0x00000000        0x9 objs/uid.o
 .bss.board_uid
                0x00000000        0xa objs/uid.o
 .text.uid_init
                0x00000000       0x14 objs/uid.o
 .text.uid_match
                0x00000000       0x42 objs/uid.o
 .text.uid_copy
                0x00000000       0x1c objs/uid.o
 .text.uid_clear
                0x00000000       0x1a objs/uid.o
 .text.uid_match_me
                0x00000000       0x20 objs/uid.o
 .text.uid_set_me
                0x00000000       0x1c objs/uid.o
 .text.str_to_uid
                0x00000000       0xd0 objs/uid.o
 .debug_info    0x00000000      0x6af objs/uid.o
 .debug_abbrev  0x00000000      0x1db objs/uid.o
 .debug_aranges
                0x00000000       0x50 objs/uid.o
 .debug_rnglists
                0x00000000       0x38 objs/uid.o
 .debug_line    0x00000000      0x28d objs/uid.o
 .debug_str     0x00000000      0x7a7 objs/uid.o
 .comment       0x00000000       0x27 objs/uid.o
 .debug_frame   0x00000000      0x10c objs/uid.o
 .ARM.attributes
                0x00000000       0x34 objs/uid.o
 .text          0x00000000        0x0 objs/crc16.o
 .data          0x00000000        0x0 objs/crc16.o
 .bss           0x00000000        0x0 objs/crc16.o
 .rodata.crc16_table
                0x00000000      0x200 objs/crc16.o
 .text.crc16_with_seed
                0x00000000       0x54 objs/crc16.o
 .text.crc16    0x00000000       0x1e objs/crc16.o
 .debug_info    0x00000000      0x15c objs/crc16.o
 .debug_abbrev  0x00000000       0xf9 objs/crc16.o
 .debug_aranges
                0x00000000       0x28 objs/crc16.o
 .debug_rnglists
                0x00000000       0x19 objs/crc16.o
 .debug_line    0x00000000       0xdb objs/crc16.o
 .debug_str     0x00000000      0x1db objs/crc16.o
 .comment       0x00000000       0x27 objs/crc16.o
 .debug_frame   0x00000000       0x5c objs/crc16.o
 .ARM.attributes
                0x00000000       0x34 objs/crc16.o
 .text          0x00000000        0x0 objs/announce.o
 .data          0x00000000        0x0 objs/announce.o
 .bss           0x00000000        0x0 objs/announce.o
 .rodata.OFFSET_TAB_CCMRx
                0x00000000        0x9 objs/announce.o
 .rodata.SHIFT_TAB_OCxx
                0x00000000        0x9 objs/announce.o
 .rodata.SHIFT_TAB_ICxx
                0x00000000        0x9 objs/announce.o
 .rodata.SHIFT_TAB_CCxP
                0x00000000        0x9 objs/announce.o
 .rodata.SHIFT_TAB_OISx
                0x00000000        0x9 objs/announce.o
 .rodata        0x00000000       0x79 objs/announce.o
 .text.announce
                0x00000000       0x6c objs/announce.o
 .debug_info    0x00000000      0x1dd objs/announce.o
 .debug_abbrev  0x00000000      0x10d objs/announce.o
 .debug_aranges
                0x00000000       0x20 objs/announce.o
 .debug_rnglists
                0x00000000       0x13 objs/announce.o
 .debug_line    0x00000000      0x10c objs/announce.o
 .debug_str     0x00000000      0x29d objs/announce.o
 .comment       0x00000000       0x27 objs/announce.o
 .debug_frame   0x00000000       0x34 objs/announce.o
 .ARM.attributes
                0x00000000       0x34 objs/announce.o
 .text          0x00000000        0x0 objs/analogin_api.o
 .data          0x00000000        0x0 objs/analogin_api.o
 .bss           0x00000000        0x0 objs/analogin_api.o
 .rodata.OFFSET_TAB_CCMRx
                0x00000000        0x9 objs/analogin_api.o
 .rodata.SHIFT_TAB_OCxx
                0x00000000        0x9 objs/analogin_api.o
 .rodata.SHIFT_TAB_ICxx
                0x00000000        0x9 objs/analogin_api.o
 .rodata.SHIFT_TAB_CCxP
                0x00000000        0x9 objs/analogin_api.o
 .rodata.SHIFT_TAB_OISx
                0x00000000        0x9 objs/analogin_api.o
 .text.analogin_read_u16
                0x00000000       0x38 objs/analogin_api.o
 .text.analogin_read
                0x00000000       0x34 objs/analogin_api.o
 .debug_info    0x00000000      0x9f4 objs/analogin_api.o
 .debug_abbrev  0x00000000      0x1a2 objs/analogin_api.o
 .debug_aranges
                0x00000000       0x28 objs/analogin_api.o
 .debug_rnglists
                0x00000000       0x19 objs/analogin_api.o
 .debug_line    0x00000000      0x182 objs/analogin_api.o
 .debug_str     0x00000000      0x92a objs/analogin_api.o
 .comment       0x00000000       0x27 objs/analogin_api.o
 .debug_frame   0x00000000       0x58 objs/analogin_api.o
 .ARM.attributes
                0x00000000       0x34 objs/analogin_api.o
 .text          0x00000000        0x0 objs/analogin_device.o
 .data          0x00000000        0x0 objs/analogin_device.o
 .bss           0x00000000        0x0 objs/analogin_device.o
 .text.LL_ADC_SetCommonPathInternalCh
                0x00000000       0x26 objs/analogin_device.o
 .rodata.OFFSET_TAB_CCMRx
                0x00000000        0x9 objs/analogin_device.o
 .rodata.SHIFT_TAB_OCxx
                0x00000000        0x9 objs/analogin_device.o
 .rodata.SHIFT_TAB_ICxx
                0x00000000        0x9 objs/analogin_device.o
 .rodata.SHIFT_TAB_CCxP
                0x00000000        0x9 objs/analogin_device.o
 .rodata.SHIFT_TAB_OISx
                0x00000000        0x9 objs/analogin_device.o
 .rodata        0x00000000       0x8f objs/analogin_device.o
 .text._analogin_init_direct
                0x00000000      0x170 objs/analogin_device.o
 .text.analogin_init
                0x00000000       0x88 objs/analogin_device.o
 .text.adc_read
                0x00000000      0x1a8 objs/analogin_device.o
 .text.analogin_pinmap
                0x00000000       0x14 objs/analogin_device.o
 .debug_info    0x00000000      0xfe3 objs/analogin_device.o
 .debug_abbrev  0x00000000      0x269 objs/analogin_device.o
 .debug_aranges
                0x00000000       0x40 objs/analogin_device.o
 .debug_rnglists
                0x00000000       0x2e objs/analogin_device.o
 .debug_line    0x00000000      0x3a4 objs/analogin_device.o
 .debug_str     0x00000000      0xcf8 objs/analogin_device.o
 .comment       0x00000000       0x27 objs/analogin_device.o
 .debug_frame   0x00000000       0xc4 objs/analogin_device.o
 .ARM.attributes
                0x00000000       0x34 objs/analogin_device.o
 .text          0x00000000        0x0 objs/dma.o
 .data          0x00000000        0x0 objs/dma.o
 .bss           0x00000000        0x0 objs/dma.o
 .debug_line    0x00000000        0x0 objs/dma.o
 .debug_str     0x00000000      0x11e objs/dma.o
 .comment       0x00000000       0x27 objs/dma.o
 .ARM.attributes
                0x00000000       0x34 objs/dma.o
 .text          0x00000000        0x0 objs/gpio.o
 .data          0x00000000        0x0 objs/gpio.o
 .bss           0x00000000        0x0 objs/gpio.o
 .rodata.OFFSET_TAB_CCMRx
                0x00000000        0x9 objs/gpio.o
 .rodata.SHIFT_TAB_OCxx
                0x00000000        0x9 objs/gpio.o
 .rodata.SHIFT_TAB_ICxx
                0x00000000        0x9 objs/gpio.o
 .rodata.SHIFT_TAB_CCxP
                0x00000000        0x9 objs/gpio.o
 .rodata.SHIFT_TAB_OISx
                0x00000000        0x9 objs/gpio.o
 .text.gpio_init_inout
                0x00000000       0x58 objs/gpio.o
 .text          0x00000000        0x0 objs/gpio_api.o
 .data          0x00000000        0x0 objs/gpio_api.o
 .bss           0x00000000        0x0 objs/gpio_api.o
 .rodata.OFFSET_TAB_CCMRx
                0x00000000        0x9 objs/gpio_api.o
 .rodata.SHIFT_TAB_OCxx
                0x00000000        0x9 objs/gpio_api.o
 .rodata.SHIFT_TAB_ICxx
                0x00000000        0x9 objs/gpio_api.o
 .rodata.SHIFT_TAB_CCxP
                0x00000000        0x9 objs/gpio_api.o
 .rodata.SHIFT_TAB_OISx
                0x00000000        0x9 objs/gpio_api.o
 .text          0x00000000        0x0 objs/gpio_irq_api.o
 .data          0x00000000        0x0 objs/gpio_irq_api.o
 .bss           0x00000000        0x0 objs/gpio_irq_api.o
 .text.__NVIC_EnableIRQ
                0x00000000       0x3c objs/gpio_irq_api.o
 .text.__NVIC_DisableIRQ
                0x00000000       0x48 objs/gpio_irq_api.o
 .text.__NVIC_ClearPendingIRQ
                0x00000000       0x3c objs/gpio_irq_api.o
 .text.__NVIC_SetVector
                0x00000000       0x34 objs/gpio_irq_api.o
 .text.LL_EXTI_EnableIT_0_31
                0x00000000       0x24 objs/gpio_irq_api.o
 .text.LL_EXTI_DisableIT_0_31
                0x00000000       0x28 objs/gpio_irq_api.o
 .text.LL_EXTI_EnableRisingTrig_0_31
                0x00000000       0x24 objs/gpio_irq_api.o
 .text.LL_EXTI_DisableRisingTrig_0_31
                0x00000000       0x28 objs/gpio_irq_api.o
 .text.LL_EXTI_IsEnabledRisingTrig_0_31
                0x00000000       0x2c objs/gpio_irq_api.o
 .text.LL_EXTI_EnableFallingTrig_0_31
                0x00000000       0x24 objs/gpio_irq_api.o
 .text.LL_EXTI_DisableFallingTrig_0_31
                0x00000000       0x28 objs/gpio_irq_api.o
 .text.LL_EXTI_IsEnabledFallingTrig_0_31
                0x00000000       0x2c objs/gpio_irq_api.o
 .rodata.OFFSET_TAB_CCMRx
                0x00000000        0x9 objs/gpio_irq_api.o
 .rodata.SHIFT_TAB_OCxx
                0x00000000        0x9 objs/gpio_irq_api.o
 .rodata.SHIFT_TAB_ICxx
                0x00000000        0x9 objs/gpio_irq_api.o
 .rodata.SHIFT_TAB_CCxP
                0x00000000        0x9 objs/gpio_irq_api.o
 .rodata.SHIFT_TAB_OISx
                0x00000000        0x9 objs/gpio_irq_api.o
 .bss.irq_handler
                0x00000000        0x4 objs/gpio_irq_api.o
 .bss.irq_channel_used
                0x00000000        0x2 objs/gpio_irq_api.o
 .bss.channels  0x00000000      0x214 objs/gpio_irq_api.o
 .rodata        0x00000000       0x7f objs/gpio_irq_api.o
 .text.handle_interrupt_in
                0x00000000      0x110 objs/gpio_irq_api.o
 .text.gpio_irq0
                0x00000000       0x10 objs/gpio_irq_api.o
 .text.gpio_irq1
                0x00000000       0x10 objs/gpio_irq_api.o
 .text.gpio_irq2
                0x00000000       0x10 objs/gpio_irq_api.o
 .text.gpio_irq3
                0x00000000       0x10 objs/gpio_irq_api.o
 .text.gpio_irq4
                0x00000000       0x10 objs/gpio_irq_api.o
 .text.gpio_irq5
                0x00000000       0x10 objs/gpio_irq_api.o
 .text.gpio_irq6
                0x00000000       0x10 objs/gpio_irq_api.o
 .text.gpio_irq_init
                0x00000000      0x200 objs/gpio_irq_api.o
 .text.gpio_irq_free
                0x00000000       0xbc objs/gpio_irq_api.o
 .text.gpio_irq_set
                0x00000000       0xc2 objs/gpio_irq_api.o
 .text.gpio_irq_enable
                0x00000000       0xdc objs/gpio_irq_api.o
 .text.gpio_irq_disable
                0x00000000       0xec objs/gpio_irq_api.o
 .debug_info    0x00000000     0x13f3 objs/gpio_irq_api.o
 .debug_abbrev  0x00000000      0x30d objs/gpio_irq_api.o
 .debug_aranges
                0x00000000       0xe0 objs/gpio_irq_api.o
 .debug_rnglists
                0x00000000       0xa9 objs/gpio_irq_api.o
 .debug_line    0x00000000      0x6b1 objs/gpio_irq_api.o
 .debug_str     0x00000000      0xeb2 objs/gpio_irq_api.o
 .comment       0x00000000       0x27 objs/gpio_irq_api.o
 .debug_frame   0x00000000      0x38c objs/gpio_irq_api.o
 .ARM.attributes
                0x00000000       0x34 objs/gpio_irq_api.o
 .text          0x00000000        0x0 objs/gpio_irq_device.o
 .data          0x00000000        0x0 objs/gpio_irq_device.o
 .bss           0x00000000        0x0 objs/gpio_irq_device.o
 .rodata.pin_lines_desc
                0x00000000       0xc0 objs/gpio_irq_device.o
 .debug_info    0x00000000      0x2c8 objs/gpio_irq_device.o
 .debug_abbrev  0x00000000       0xbf objs/gpio_irq_device.o
 .debug_aranges
                0x00000000       0x18 objs/gpio_irq_device.o
 .debug_line    0x00000000       0xc0 objs/gpio_irq_device.o
 .debug_str     0x00000000      0x5ff objs/gpio_irq_device.o
 .comment       0x00000000       0x27 objs/gpio_irq_device.o
 .ARM.attributes
                0x00000000       0x34 objs/gpio_irq_device.o
 .text          0x00000000        0x0 objs/PeripheralPins.o
 .data          0x00000000        0x0 objs/PeripheralPins.o
 .bss           0x00000000        0x0 objs/PeripheralPins.o
 .rodata.PinMap_ADC
                0x00000000       0x84 objs/PeripheralPins.o
 .rodata.PinMap_ADC_Internal
                0x00000000       0x24 objs/PeripheralPins.o
 .rodata.PinMap_DAC
                0x00000000       0x24 objs/PeripheralPins.o
 .rodata.PinMap_I2C_SDA
                0x00000000       0x54 objs/PeripheralPins.o
 .rodata.PinMap_I2C_SCL
                0x00000000       0x54 objs/PeripheralPins.o
 .rodata.PinMap_PWM
                0x00000000       0xf0 objs/PeripheralPins.o
 .rodata.PinMap_UART_RTS
                0x00000000       0x6c objs/PeripheralPins.o
 .rodata.PinMap_UART_CTS
                0x00000000       0x60 objs/PeripheralPins.o
 .rodata.PinMap_SPI_MOSI
                0x00000000       0x48 objs/PeripheralPins.o
 .rodata.PinMap_SPI_MISO
                0x00000000       0x48 objs/PeripheralPins.o
 .rodata.PinMap_SPI_SCLK
                0x00000000       0x54 objs/PeripheralPins.o
 .rodata.PinMap_SPI_SSEL
                0x00000000       0x60 objs/PeripheralPins.o
 .rodata.PinMap_CAN_RD
                0x00000000       0x24 objs/PeripheralPins.o
 .rodata.PinMap_CAN_TD
                0x00000000       0x24 objs/PeripheralPins.o
 .rodata.PinMap_QSPI_DATA0
                0x00000000       0x18 objs/PeripheralPins.o
 .rodata.PinMap_QSPI_DATA1
                0x00000000       0x18 objs/PeripheralPins.o
 .rodata.PinMap_QSPI_DATA2
                0x00000000       0x18 objs/PeripheralPins.o
 .rodata.PinMap_QSPI_DATA3
                0x00000000       0x18 objs/PeripheralPins.o
 .rodata.PinMap_QSPI_SCLK
                0x00000000       0x24 objs/PeripheralPins.o
 .rodata.PinMap_QSPI_SSEL
                0x00000000       0x24 objs/PeripheralPins.o
 .text          0x00000000        0x0 objs/pinmap.o
 .data          0x00000000        0x0 objs/pinmap.o
 .bss           0x00000000        0x0 objs/pinmap.o
 .text          0x00000000        0x0 objs/mbed_pinmap_common.o
 .data          0x00000000        0x0 objs/mbed_pinmap_common.o
 .bss           0x00000000        0x0 objs/mbed_pinmap_common.o
 .text.pinmap_pinout
                0x00000000       0x60 objs/mbed_pinmap_common.o
 .text.pinmap_function
                0x00000000       0x48 objs/mbed_pinmap_common.o
 .text.pinmap_find_peripheral_pins
                0x00000000      0x126 objs/mbed_pinmap_common.o
 .text.pinmap_list_has_pin
                0x00000000       0x4a objs/mbed_pinmap_common.o
 .text.pinmap_list_has_peripheral
                0x00000000       0x44 objs/mbed_pinmap_common.o
 .text          0x00000000        0x0 objs/mbed_us_ticker_api.o
 .data          0x00000000        0x0 objs/mbed_us_ticker_api.o
 .bss           0x00000000        0x0 objs/mbed_us_ticker_api.o
 .rodata.OFFSET_TAB_CCMRx
                0x00000000        0x9 objs/mbed_us_ticker_api.o
 .rodata.SHIFT_TAB_OCxx
                0x00000000        0x9 objs/mbed_us_ticker_api.o
 .rodata.SHIFT_TAB_ICxx
                0x00000000        0x9 objs/mbed_us_ticker_api.o
 .rodata.SHIFT_TAB_CCxP
                0x00000000        0x9 objs/mbed_us_ticker_api.o
 .rodata.SHIFT_TAB_OISx
                0x00000000        0x9 objs/mbed_us_ticker_api.o
 .bss.events    0x00000000       0x38 objs/mbed_us_ticker_api.o
 .data.irq_handler
                0x00000000        0x4 objs/mbed_us_ticker_api.o
 .bss._us_ticker_initialized
                0x00000000        0x1 objs/mbed_us_ticker_api.o
 .text.note_us_ticker_init
                0x00000000       0x24 objs/mbed_us_ticker_api.o
 .text.note_us_ticker_free
                0x00000000       0x24 objs/mbed_us_ticker_api.o
 .rodata.us_interface
                0x00000000       0x24 objs/mbed_us_ticker_api.o
 .rodata.us_data
                0x00000000        0x8 objs/mbed_us_ticker_api.o
 .text.get_us_ticker_data
                0x00000000       0x14 objs/mbed_us_ticker_api.o
 .text.set_us_ticker_irq_handler
                0x00000000       0x28 objs/mbed_us_ticker_api.o
 .text.us_ticker_irq_handler
                0x00000000       0x20 objs/mbed_us_ticker_api.o
 .debug_info    0x00000000      0x569 objs/mbed_us_ticker_api.o
 .debug_abbrev  0x00000000      0x231 objs/mbed_us_ticker_api.o
 .debug_aranges
                0x00000000       0x40 objs/mbed_us_ticker_api.o
 .debug_rnglists
                0x00000000       0x2b objs/mbed_us_ticker_api.o
 .debug_line    0x00000000      0x1c7 objs/mbed_us_ticker_api.o
 .debug_str     0x00000000      0x5ca objs/mbed_us_ticker_api.o
 .comment       0x00000000       0x27 objs/mbed_us_ticker_api.o
 .debug_frame   0x00000000       0xbc objs/mbed_us_ticker_api.o
 .ARM.attributes
                0x00000000       0x34 objs/mbed_us_ticker_api.o
 .text          0x00000000        0x0 objs/serial_api.o
 .data          0x00000000        0x0 objs/serial_api.o
 .bss           0x00000000        0x0 objs/serial_api.o
 .text.LL_USART_IsEnabled
                0x00000000       0x26 objs/serial_api.o
 .text.LL_USART_IsActiveFlag_TC
                0x00000000       0x26 objs/serial_api.o
 .rodata.OFFSET_TAB_CCMRx
                0x00000000        0x9 objs/serial_api.o
 .rodata.SHIFT_TAB_OCxx
                0x00000000        0x9 objs/serial_api.o
 .rodata.SHIFT_TAB_ICxx
                0x00000000        0x9 objs/serial_api.o
 .rodata.SHIFT_TAB_CCxP
                0x00000000        0x9 objs/serial_api.o
 .rodata.SHIFT_TAB_OISx
                0x00000000        0x9 objs/serial_api.o
 .text.serial_free
                0x00000000      0x110 objs/serial_api.o
 .text.serial_tx_pinmap
                0x00000000       0x14 objs/serial_api.o
 .text.serial_rx_pinmap
                0x00000000       0x14 objs/serial_api.o
 .text.serial_cts_pinmap
                0x00000000       0x14 objs/serial_api.o
 .text.serial_rts_pinmap
                0x00000000       0x14 objs/serial_api.o
 .text.serial_readable
                0x00000000       0x58 objs/serial_api.o
 .text.serial_writable
                0x00000000       0x44 objs/serial_api.o
 .text.serial_pinout_tx
                0x00000000       0x24 objs/serial_api.o
 .text.serial_break_clear
                0x00000000       0x14 objs/serial_api.o
 .text.serial_is_tx_ongoing
                0x00000000       0xa4 objs/serial_api.o
 .text.serial_read
                0x00000000       0x28 objs/serial_api.o
 .text.serial_read_signal
                0x00000000       0x2a objs/serial_api.o
 .rodata.PinMap_UART_CTS.1
                0x00000000        0xc objs/serial_api.o
 .rodata.PinMap_UART_RTS.0
                0x00000000        0xc objs/serial_api.o
 .text          0x00000000        0x0 objs/serial_device.o
 .data          0x00000000        0x0 objs/serial_device.o
 .bss           0x00000000        0x0 objs/serial_device.o
 .text.__NVIC_EnableIRQ
                0x00000000       0x3c objs/serial_device.o
 .text.__NVIC_DisableIRQ
                0x00000000       0x48 objs/serial_device.o
 .text.__NVIC_ClearPendingIRQ
                0x00000000       0x3c objs/serial_device.o
 .text.__NVIC_SetPriority
                0x00000000       0x54 objs/serial_device.o
 .text.__NVIC_SetVector
                0x00000000       0x34 objs/serial_device.o
 .text.LL_LPUART_IsEnabledIT_RXNE
                0x00000000       0x26 objs/serial_device.o
 .text.LL_LPUART_IsEnabledIT_TXE
                0x00000000       0x26 objs/serial_device.o
 .rodata.OFFSET_TAB_CCMRx
                0x00000000        0x9 objs/serial_device.o
 .rodata.SHIFT_TAB_OCxx
                0x00000000        0x9 objs/serial_device.o
 .rodata.SHIFT_TAB_ICxx
                0x00000000        0x9 objs/serial_device.o
 .rodata.SHIFT_TAB_CCxP
                0x00000000        0x9 objs/serial_device.o
 .rodata.SHIFT_TAB_OISx
                0x00000000        0x9 objs/serial_device.o
 .bss.serial_irq_ids
                0x00000000        0xc objs/serial_device.o
 .bss.irq_handler
                0x00000000        0x4 objs/serial_device.o
 .text.uart_irq
                0x00000000      0x224 objs/serial_device.o
 .text.uart1_irq
                0x00000000       0x14 objs/serial_device.o
 .text.uart2_irq
                0x00000000       0x14 objs/serial_device.o
 .text.uart3_irq
                0x00000000       0x14 objs/serial_device.o
 .text.lpuart1_irq
                0x00000000       0x14 objs/serial_device.o
 .text.serial_irq_handler
                0x00000000       0x38 objs/serial_device.o
 .text.serial_irq_set
                0x00000000      0x160 objs/serial_device.o
 .text.serial_getc
                0x00000000       0xc8 objs/serial_device.o
 .text.serial_putc
                0x00000000       0x4c objs/serial_device.o
 .text.serial_clear
                0x00000000       0x3c objs/serial_device.o
 .text.serial_break_set
                0x00000000       0x30 objs/serial_device.o
 .text.serial_tx_buffer_set
                0x00000000       0x36 objs/serial_device.o
 .text.serial_rx_buffer_set
                0x00000000       0x36 objs/serial_device.o
 .text.serial_enable_event
                0x00000000       0x40 objs/serial_device.o
 .text.serial_get_irq_n
                0x00000000       0x78 objs/serial_device.o
 .rodata        0x00000000       0x6e objs/serial_device.o
 .text.serial_tx_asynch
                0x00000000       0xec objs/serial_device.o
 .text.serial_rx_asynch
                0x00000000       0xf8 objs/serial_device.o
 .text.serial_tx_active
                0x00000000       0x5c objs/serial_device.o
 .text.serial_rx_active
                0x00000000       0x5c objs/serial_device.o
 .text.HAL_UART_ErrorCallback
                0x00000000       0x7a objs/serial_device.o
 .text.serial_irq_handler_asynch
                0x00000000      0x1b8 objs/serial_device.o
 .text.serial_tx_abort_asynch
                0x00000000       0x6c objs/serial_device.o
 .text.serial_rx_abort_asynch
                0x00000000       0x90 objs/serial_device.o
 .text          0x00000000        0x0 objs/i2c_api.o
 .data          0x00000000        0x0 objs/i2c_api.o
 .bss           0x00000000        0x0 objs/i2c_api.o
 .text.__NVIC_EnableIRQ
                0x00000000       0x3c objs/i2c_api.o
 .text.__NVIC_SetPriority
                0x00000000       0x54 objs/i2c_api.o
 .text.__NVIC_SetVector
                0x00000000       0x34 objs/i2c_api.o
 .rodata.OFFSET_TAB_CCMRx
                0x00000000        0x9 objs/i2c_api.o
 .rodata.SHIFT_TAB_OCxx
                0x00000000        0x9 objs/i2c_api.o
 .rodata.SHIFT_TAB_ICxx
                0x00000000        0x9 objs/i2c_api.o
 .rodata.SHIFT_TAB_CCxP
                0x00000000        0x9 objs/i2c_api.o
 .rodata.SHIFT_TAB_OISx
                0x00000000        0x9 objs/i2c_api.o
 .bss.i2c_handles
                0x00000000       0x14 objs/i2c_api.o
 .text.i2c1_irq
                0x00000000       0x24 objs/i2c_api.o
 .text.i2c2_irq
                0x00000000       0x24 objs/i2c_api.o
 .text.i2c3_irq
                0x00000000       0x24 objs/i2c_api.o
 .text.i2c_ev_err_enable
                0x00000000       0x6e objs/i2c_api.o
 .text.i2c_ev_err_disable
                0x00000000       0x38 objs/i2c_api.o
 .text.i2c_get_irq_handler
                0x00000000       0x70 objs/i2c_api.o
 .text.i2c_hw_reset
                0x00000000       0xd8 objs/i2c_api.o
 .text.i2c_sw_reset
                0x00000000       0x4e objs/i2c_api.o
 .rodata        0x00000000       0x91 objs/i2c_api.o
 .text.i2c_init_internal
                0x00000000      0x1a4 objs/i2c_api.o
 .text.i2c_deinit_internal
                0x00000000       0xb4 objs/i2c_api.o
 .text._i2c_init_direct
                0x00000000       0x24 objs/i2c_api.o
 .text.i2c_init
                0x00000000       0x88 objs/i2c_api.o
 .text.i2c_free
                0x00000000       0x16 objs/i2c_api.o
 .text.i2c_frequency
                0x00000000      0x1a8 objs/i2c_api.o
 .text.get_i2c_obj
                0x00000000       0x20 objs/i2c_api.o
 .text.i2c_reset
                0x00000000       0x1e objs/i2c_api.o
 .text.i2c_is_ready_for_transaction_start
                0x00000000       0x3a objs/i2c_api.o
 .text.prep_for_restart_if_needed
                0x00000000       0x70 objs/i2c_api.o
 .text.get_hal_xfer_options
                0x00000000       0x26 objs/i2c_api.o
 .text.i2c_start
                0x00000000       0x24 objs/i2c_api.o
 .text.i2c_stop
                0x00000000      0x128 objs/i2c_api.o
 .text.i2c_byte_read
                0x00000000       0xc6 objs/i2c_api.o
 .text.i2c_byte_write
                0x00000000      0x1b8 objs/i2c_api.o
 .text.i2c_read
                0x00000000      0x144 objs/i2c_api.o
 .text.i2c_write
                0x00000000      0x144 objs/i2c_api.o
 .text.HAL_I2C_MasterTxCpltCallback
                0x00000000       0x30 objs/i2c_api.o
 .text.HAL_I2C_MasterRxCpltCallback
                0x00000000       0x30 objs/i2c_api.o
 .text.HAL_I2C_ErrorCallback
                0x00000000       0x68 objs/i2c_api.o
 .text.i2c_master_sda_pinmap
                0x00000000       0x14 objs/i2c_api.o
 .text.i2c_master_scl_pinmap
                0x00000000       0x14 objs/i2c_api.o
 .text.i2c_slave_sda_pinmap
                0x00000000       0x14 objs/i2c_api.o
 .text.i2c_slave_scl_pinmap
                0x00000000       0x14 objs/i2c_api.o
 .debug_info    0x00000000     0x1ecc objs/i2c_api.o
 .debug_abbrev  0x00000000      0x39c objs/i2c_api.o
 .debug_aranges
                0x00000000      0x130 objs/i2c_api.o
 .debug_rnglists
                0x00000000       0xf5 objs/i2c_api.o
 .debug_line    0x00000000      0xbd7 objs/i2c_api.o
 .debug_str     0x00000000     0x1629 objs/i2c_api.o
 .comment       0x00000000       0x27 objs/i2c_api.o
 .debug_frame   0x00000000      0x524 objs/i2c_api.o
 .ARM.attributes
                0x00000000       0x34 objs/i2c_api.o
 .text          0x00000000        0x0 objs/port_stubs.o
 .data          0x00000000        0x0 objs/port_stubs.o
 .bss           0x00000000        0x0 objs/port_stubs.o
 .text.clock_read
                0x00000000       0x10 objs/port_stubs.o
 .text.rtc_reset_time
                0x00000000        0xe objs/port_stubs.o
 .text.kinetis_flash_erase_sector
                0x00000000       0x16 objs/port_stubs.o
 .text.kinetis_flash_write
                0x00000000       0x1a objs/port_stubs.o
 .text.kinetis_flash_read
                0x00000000       0x1a objs/port_stubs.o
 .bss.rtc_clock
                0x00000000        0x4 objs/port_stubs.o
 .bss.epoch_clock
                0x00000000        0x4 objs/port_stubs.o
 .text.core_util_critical_section_exit
                0x00000000        0xe objs/port_stubs.o
 .text.core_util_critical_section_enter
                0x00000000        0xe objs/port_stubs.o
 .text.bus_frequency
                0x00000000       0x14 objs/port_stubs.o
 .text.stm_flash_erase_sector
                0x00000000       0x16 objs/port_stubs.o
 .text.stm_flash_write
                0x00000000       0x1a objs/port_stubs.o
 .text.stm_flash_read
                0x00000000       0x1a objs/port_stubs.o
 .text.stm_flash_keep_on
                0x00000000       0x16 objs/port_stubs.o
 .data.stm_flash_device
                0x00000000       0x18 objs/port_stubs.o
 .text.port_delay_ms
                0x00000000       0x18 objs/port_stubs.o
 .text          0x00000000        0x0 objs/serial_api_stubs.o
 .data          0x00000000        0x0 objs/serial_api_stubs.o
 .bss           0x00000000        0x0 objs/serial_api_stubs.o
 .rodata.OFFSET_TAB_CCMRx
                0x00000000        0x9 objs/serial_api_stubs.o
 .rodata.SHIFT_TAB_OCxx
                0x00000000        0x9 objs/serial_api_stubs.o
 .rodata.SHIFT_TAB_ICxx
                0x00000000        0x9 objs/serial_api_stubs.o
 .rodata.SHIFT_TAB_CCxP
                0x00000000        0x9 objs/serial_api_stubs.o
 .rodata.SHIFT_TAB_OISx
                0x00000000        0x9 objs/serial_api_stubs.o
 .bss.serial_semihost
                0x00000000        0x1 objs/serial_api_stubs.o
 .text.serial_available
                0x00000000       0x44 objs/serial_api_stubs.o
 .text.serial_read
                0x00000000       0x24 objs/serial_api_stubs.o
 .text.serial_read_timeout
                0x00000000       0xe4 objs/serial_api_stubs.o
 .text.serial_read_signal
                0x00000000       0x2a objs/serial_api_stubs.o
 .text.serial_read_timeout_signal
                0x00000000       0x30 objs/serial_api_stubs.o
 .text.serial_write
                0x00000000       0x44 objs/serial_api_stubs.o
 .text.serial_hold
                0x00000000       0x14 objs/serial_api_stubs.o
 .text.serial_release
                0x00000000       0x14 objs/serial_api_stubs.o
 .text.serial_enable_dma_rx
                0x00000000       0x18 objs/serial_api_stubs.o
 .text.serial_enable_dma_tx
                0x00000000       0x14 objs/serial_api_stubs.o
 .text.serial_suspend_dma_rx
                0x00000000       0x14 objs/serial_api_stubs.o
 .text.serial_resume_dma_rx
                0x00000000       0x14 objs/serial_api_stubs.o
 .text.serial_power_off
                0x00000000       0x3a objs/serial_api_stubs.o
 .text.serial_console_init
                0x00000000       0x16 objs/serial_api_stubs.o
 .text          0x00000000        0x0 objs/serial_wire_debug.o
 .data          0x00000000        0x0 objs/serial_wire_debug.o
 .bss           0x00000000        0x0 objs/serial_wire_debug.o
 .text.SWO_Init
                0x00000000       0x40 objs/serial_wire_debug.o
 .text.ITM_SendString
                0x00000000       0x2a objs/serial_wire_debug.o
 .text.ITM_Printf
                0x00000000       0x36 objs/serial_wire_debug.o
 .text          0x00000000        0x0 objs/stm32l4xx_hal.o
 .data          0x00000000        0x0 objs/stm32l4xx_hal.o
 .bss           0x00000000        0x0 objs/stm32l4xx_hal.o
 .text.HAL_DeInit
                0x00000000       0x68 objs/stm32l4xx_hal.o
 .text.HAL_MspDeInit
                0x00000000        0xe objs/stm32l4xx_hal.o
 .text.HAL_GetTick
                0x00000000       0x18 objs/stm32l4xx_hal.o
 .text.HAL_GetTickPrio
                0x00000000       0x18 objs/stm32l4xx_hal.o
 .text.HAL_SetTickFreq
                0x00000000       0x50 objs/stm32l4xx_hal.o
 .text.HAL_GetTickFreq
                0x00000000       0x18 objs/stm32l4xx_hal.o
 .text.HAL_Delay
                0x00000000       0x48 objs/stm32l4xx_hal.o
 .text.HAL_SuspendTick
                0x00000000       0x20 objs/stm32l4xx_hal.o
 .text.HAL_ResumeTick
                0x00000000       0x20 objs/stm32l4xx_hal.o
 .text.HAL_GetHalVersion
                0x00000000       0x14 objs/stm32l4xx_hal.o
 .text.HAL_GetREVID
                0x00000000       0x1c objs/stm32l4xx_hal.o
 .text.HAL_GetDEVID
                0x00000000       0x1c objs/stm32l4xx_hal.o
 .text.HAL_GetUIDw0
                0x00000000       0x18 objs/stm32l4xx_hal.o
 .text.HAL_GetUIDw1
                0x00000000       0x18 objs/stm32l4xx_hal.o
 .text.HAL_GetUIDw2
                0x00000000       0x18 objs/stm32l4xx_hal.o
 .text.HAL_DBGMCU_EnableDBGSleepMode
                0x00000000       0x20 objs/stm32l4xx_hal.o
 .text.HAL_DBGMCU_DisableDBGSleepMode
                0x00000000       0x20 objs/stm32l4xx_hal.o
 .text.HAL_DBGMCU_EnableDBGStopMode
                0x00000000       0x20 objs/stm32l4xx_hal.o
 .text.HAL_DBGMCU_DisableDBGStopMode
                0x00000000       0x20 objs/stm32l4xx_hal.o
 .text.HAL_DBGMCU_EnableDBGStandbyMode
                0x00000000       0x20 objs/stm32l4xx_hal.o
 .text.HAL_DBGMCU_DisableDBGStandbyMode
                0x00000000       0x20 objs/stm32l4xx_hal.o
 .text.HAL_SYSCFG_SRAM2Erase
                0x00000000       0x28 objs/stm32l4xx_hal.o
 .text.HAL_SYSCFG_EnableMemorySwappingBank
                0x00000000       0x18 objs/stm32l4xx_hal.o
 .text.HAL_SYSCFG_DisableMemorySwappingBank
                0x00000000       0x18 objs/stm32l4xx_hal.o
 .text.HAL_SYSCFG_VREFBUF_VoltageScalingConfig
                0x00000000       0x28 objs/stm32l4xx_hal.o
 .text.HAL_SYSCFG_VREFBUF_HighImpedanceConfig
                0x00000000       0x28 objs/stm32l4xx_hal.o
 .text.HAL_SYSCFG_VREFBUF_TrimmingConfig
                0x00000000       0x28 objs/stm32l4xx_hal.o
 .text.HAL_SYSCFG_EnableVREFBUF
                0x00000000       0x48 objs/stm32l4xx_hal.o
 .text.HAL_SYSCFG_DisableVREFBUF
                0x00000000       0x20 objs/stm32l4xx_hal.o
 .text.HAL_SYSCFG_EnableIOAnalogSwitchBooster
                0x00000000       0x20 objs/stm32l4xx_hal.o
 .text.HAL_SYSCFG_DisableIOAnalogSwitchBooster
                0x00000000       0x20 objs/stm32l4xx_hal.o
 .text          0x00000000        0x0 objs/stm32l4xx_hal_cortex.o
 .data          0x00000000        0x0 objs/stm32l4xx_hal_cortex.o
 .bss           0x00000000        0x0 objs/stm32l4xx_hal_cortex.o
 .text.__NVIC_EnableIRQ
                0x00000000       0x3c objs/stm32l4xx_hal_cortex.o
 .text.__NVIC_DisableIRQ
                0x00000000       0x48 objs/stm32l4xx_hal_cortex.o
 .text.__NVIC_GetPendingIRQ
                0x00000000       0x44 objs/stm32l4xx_hal_cortex.o
 .text.__NVIC_SetPendingIRQ
                0x00000000       0x3c objs/stm32l4xx_hal_cortex.o
 .text.__NVIC_ClearPendingIRQ
                0x00000000       0x3c objs/stm32l4xx_hal_cortex.o
 .text.__NVIC_GetActive
                0x00000000       0x44 objs/stm32l4xx_hal_cortex.o
 .text.__NVIC_GetPriority
                0x00000000       0x50 objs/stm32l4xx_hal_cortex.o
 .text.NVIC_DecodePriority
                0x00000000       0x6e objs/stm32l4xx_hal_cortex.o
 .text.__NVIC_SystemReset
                0x00000000       0x2c objs/stm32l4xx_hal_cortex.o
 .text.HAL_NVIC_EnableIRQ
                0x00000000       0x1c objs/stm32l4xx_hal_cortex.o
 .text.HAL_NVIC_DisableIRQ
                0x00000000       0x1c objs/stm32l4xx_hal_cortex.o
 .text.HAL_NVIC_SystemReset
                0x00000000        0x8 objs/stm32l4xx_hal_cortex.o
 .text.HAL_NVIC_GetPriorityGrouping
                0x00000000        0xe objs/stm32l4xx_hal_cortex.o
 .text.HAL_NVIC_GetPriority
                0x00000000       0x2c objs/stm32l4xx_hal_cortex.o
 .text.HAL_NVIC_SetPendingIRQ
                0x00000000       0x1c objs/stm32l4xx_hal_cortex.o
 .text.HAL_NVIC_GetPendingIRQ
                0x00000000       0x1e objs/stm32l4xx_hal_cortex.o
 .text.HAL_NVIC_ClearPendingIRQ
                0x00000000       0x1c objs/stm32l4xx_hal_cortex.o
 .text.HAL_NVIC_GetActive
                0x00000000       0x1e objs/stm32l4xx_hal_cortex.o
 .text.HAL_SYSTICK_CLKSourceConfig
                0x00000000       0x38 objs/stm32l4xx_hal_cortex.o
 .text.HAL_SYSTICK_IRQHandler
                0x00000000        0xc objs/stm32l4xx_hal_cortex.o
 .text.HAL_SYSTICK_Callback
                0x00000000        0xe objs/stm32l4xx_hal_cortex.o
 .text.HAL_MPU_Enable
                0x00000000       0x30 objs/stm32l4xx_hal_cortex.o
 .text.HAL_MPU_Disable
                0x00000000       0x20 objs/stm32l4xx_hal_cortex.o
 .text.HAL_MPU_ConfigRegion
                0x00000000       0x88 objs/stm32l4xx_hal_cortex.o
 .text          0x00000000        0x0 objs/stm32l4xx_hal_pwr.o
 .data          0x00000000        0x0 objs/stm32l4xx_hal_pwr.o
 .bss           0x00000000        0x0 objs/stm32l4xx_hal_pwr.o
 .text.HAL_PWR_DeInit
                0x00000000       0x2c objs/stm32l4xx_hal_pwr.o
 .text.HAL_PWR_DisableBkUpAccess
                0x00000000       0x20 objs/stm32l4xx_hal_pwr.o
 .text.HAL_PWR_ConfigPVD
                0x00000000       0xc0 objs/stm32l4xx_hal_pwr.o
 .text.HAL_PWR_EnablePVD
                0x00000000       0x20 objs/stm32l4xx_hal_pwr.o
 .text.HAL_PWR_DisablePVD
                0x00000000       0x20 objs/stm32l4xx_hal_pwr.o
 .text.HAL_PWR_EnableWakeUpPin
                0x00000000       0x40 objs/stm32l4xx_hal_pwr.o
 .text.HAL_PWR_DisableWakeUpPin
                0x00000000       0x2c objs/stm32l4xx_hal_pwr.o
 .text.HAL_PWR_EnterSLEEPMode
                0x00000000       0x68 objs/stm32l4xx_hal_pwr.o
 .text.HAL_PWR_EnterSTOPMode
                0x00000000       0x2e objs/stm32l4xx_hal_pwr.o
 .text.HAL_PWR_EnterSTANDBYMode
                0x00000000       0x34 objs/stm32l4xx_hal_pwr.o
 .text.HAL_PWR_EnableSleepOnExit
                0x00000000       0x20 objs/stm32l4xx_hal_pwr.o
 .text.HAL_PWR_DisableSleepOnExit
                0x00000000       0x20 objs/stm32l4xx_hal_pwr.o
 .text.HAL_PWR_EnableSEVOnPend
                0x00000000       0x20 objs/stm32l4xx_hal_pwr.o
 .text.HAL_PWR_DisableSEVOnPend
                0x00000000       0x20 objs/stm32l4xx_hal_pwr.o
 .text.HAL_PWR_PVDCallback
                0x00000000        0xe objs/stm32l4xx_hal_pwr.o
 .text          0x00000000        0x0 objs/stm32l4xx_hal_pwr_ex.o
 .data          0x00000000        0x0 objs/stm32l4xx_hal_pwr_ex.o
 .bss           0x00000000        0x0 objs/stm32l4xx_hal_pwr_ex.o
 .text.HAL_PWREx_GetVoltageRange
                0x00000000       0x1c objs/stm32l4xx_hal_pwr_ex.o
 .text.HAL_PWREx_ControlVoltageScaling
                0x00000000       0xac objs/stm32l4xx_hal_pwr_ex.o
 .text.HAL_PWREx_EnableBatteryCharging
                0x00000000       0x34 objs/stm32l4xx_hal_pwr_ex.o
 .text.HAL_PWREx_DisableBatteryCharging
                0x00000000       0x20 objs/stm32l4xx_hal_pwr_ex.o
 .text.HAL_PWREx_EnableInternalWakeUpLine
                0x00000000       0x20 objs/stm32l4xx_hal_pwr_ex.o
 .text.HAL_PWREx_DisableInternalWakeUpLine
                0x00000000       0x20 objs/stm32l4xx_hal_pwr_ex.o
 .text.HAL_PWREx_EnableGPIOPullUp
                0x00000000      0x110 objs/stm32l4xx_hal_pwr_ex.o
 .text.HAL_PWREx_DisableGPIOPullUp
                0x00000000       0xbc objs/stm32l4xx_hal_pwr_ex.o
 .text.HAL_PWREx_EnableGPIOPullDown
                0x00000000      0x110 objs/stm32l4xx_hal_pwr_ex.o
 .text.HAL_PWREx_DisableGPIOPullDown
                0x00000000       0xc0 objs/stm32l4xx_hal_pwr_ex.o
 .text.HAL_PWREx_EnablePullUpPullDownConfig
                0x00000000       0x20 objs/stm32l4xx_hal_pwr_ex.o
 .text.HAL_PWREx_DisablePullUpPullDownConfig
                0x00000000       0x20 objs/stm32l4xx_hal_pwr_ex.o
 .text.HAL_PWREx_EnableSRAM2ContentRetention
                0x00000000       0x10 objs/stm32l4xx_hal_pwr_ex.o
 .text.HAL_PWREx_DisableSRAM2ContentRetention
                0x00000000        0xe objs/stm32l4xx_hal_pwr_ex.o
 .text.HAL_PWREx_SetSRAM2ContentRetention
                0x00000000       0x48 objs/stm32l4xx_hal_pwr_ex.o
 .text.HAL_PWREx_EnablePVM3
                0x00000000       0x20 objs/stm32l4xx_hal_pwr_ex.o
 .text.HAL_PWREx_DisablePVM3
                0x00000000       0x20 objs/stm32l4xx_hal_pwr_ex.o
 .text.HAL_PWREx_EnablePVM4
                0x00000000       0x20 objs/stm32l4xx_hal_pwr_ex.o
 .text.HAL_PWREx_DisablePVM4
                0x00000000       0x20 objs/stm32l4xx_hal_pwr_ex.o
 .text.HAL_PWREx_ConfigPVM
                0x00000000      0x15c objs/stm32l4xx_hal_pwr_ex.o
 .text.HAL_PWREx_EnableLowPowerRunMode
                0x00000000       0x20 objs/stm32l4xx_hal_pwr_ex.o
 .text.HAL_PWREx_DisableLowPowerRunMode
                0x00000000       0x70 objs/stm32l4xx_hal_pwr_ex.o
 .text.HAL_PWREx_EnterSTOP0Mode
                0x00000000       0x54 objs/stm32l4xx_hal_pwr_ex.o
 .text.HAL_PWREx_EnterSTOP1Mode
                0x00000000       0x58 objs/stm32l4xx_hal_pwr_ex.o
 .text.HAL_PWREx_EnterSTOP2Mode
                0x00000000       0x58 objs/stm32l4xx_hal_pwr_ex.o
 .text.HAL_PWREx_EnterSHUTDOWNMode
                0x00000000       0x34 objs/stm32l4xx_hal_pwr_ex.o
 .text.HAL_PWREx_PVD_PVM_IRQHandler
                0x00000000       0x50 objs/stm32l4xx_hal_pwr_ex.o
 .text.HAL_PWREx_PVM3Callback
                0x00000000        0xe objs/stm32l4xx_hal_pwr_ex.o
 .text.HAL_PWREx_PVM4Callback
                0x00000000        0xe objs/stm32l4xx_hal_pwr_ex.o
 .debug_info    0x00000000      0x8a3 objs/stm32l4xx_hal_pwr_ex.o
 .debug_abbrev  0x00000000      0x1f7 objs/stm32l4xx_hal_pwr_ex.o
 .debug_aranges
                0x00000000      0x100 objs/stm32l4xx_hal_pwr_ex.o
 .debug_rnglists
                0x00000000       0xc1 objs/stm32l4xx_hal_pwr_ex.o
 .debug_line    0x00000000      0x578 objs/stm32l4xx_hal_pwr_ex.o
 .debug_str     0x00000000      0x719 objs/stm32l4xx_hal_pwr_ex.o
 .comment       0x00000000       0x27 objs/stm32l4xx_hal_pwr_ex.o
 .debug_frame   0x00000000      0x404 objs/stm32l4xx_hal_pwr_ex.o
 .ARM.attributes
                0x00000000       0x34 objs/stm32l4xx_hal_pwr_ex.o
 .text          0x00000000        0x0 objs/stm32l4xx_hal_rtc.o
 .data          0x00000000        0x0 objs/stm32l4xx_hal_rtc.o
 .bss           0x00000000        0x0 objs/stm32l4xx_hal_rtc.o
 .text.HAL_RTC_Init
                0x00000000       0xf6 objs/stm32l4xx_hal_rtc.o
 .text.HAL_RTC_DeInit
                0x00000000       0xe8 objs/stm32l4xx_hal_rtc.o
 .text.HAL_RTC_MspInit
                0x00000000       0x14 objs/stm32l4xx_hal_rtc.o
 .text.HAL_RTC_MspDeInit
                0x00000000       0x14 objs/stm32l4xx_hal_rtc.o
 .text.HAL_RTC_SetTime
                0x00000000      0x13a objs/stm32l4xx_hal_rtc.o
 .text.HAL_RTC_GetTime
                0x00000000       0xb8 objs/stm32l4xx_hal_rtc.o
 .text.HAL_RTC_SetDate
                0x00000000      0x10e objs/stm32l4xx_hal_rtc.o
 .text.HAL_RTC_GetDate
                0x00000000       0x9a objs/stm32l4xx_hal_rtc.o
 .text.HAL_RTC_SetAlarm
                0x00000000      0x25a objs/stm32l4xx_hal_rtc.o
 .text.HAL_RTC_SetAlarm_IT
                0x00000000      0x278 objs/stm32l4xx_hal_rtc.o
 .text.HAL_RTC_DeactivateAlarm
                0x00000000      0x128 objs/stm32l4xx_hal_rtc.o
 .text.HAL_RTC_GetAlarm
                0x00000000      0x180 objs/stm32l4xx_hal_rtc.o
 .text.HAL_RTC_DST_Add1Hour
                0x00000000       0x3c objs/stm32l4xx_hal_rtc.o
 .text.HAL_RTC_DST_Sub1Hour
                0x00000000       0x3c objs/stm32l4xx_hal_rtc.o
 .text.HAL_RTC_DST_SetStoreOperation
                0x00000000       0x3c objs/stm32l4xx_hal_rtc.o
 .text.HAL_RTC_DST_ClearStoreOperation
                0x00000000       0x3c objs/stm32l4xx_hal_rtc.o
 .text.HAL_RTC_DST_ReadStoreOperation
                0x00000000       0x20 objs/stm32l4xx_hal_rtc.o
 .text.HAL_RTC_AlarmIRQHandler
                0x00000000       0x8c objs/stm32l4xx_hal_rtc.o
 .text.HAL_RTC_AlarmAEventCallback
                0x00000000       0x14 objs/stm32l4xx_hal_rtc.o
 .text.HAL_RTC_PollForAlarmAEvent
                0x00000000       0x6e objs/stm32l4xx_hal_rtc.o
 .text.HAL_RTC_WaitForSynchro
                0x00000000       0x4c objs/stm32l4xx_hal_rtc.o
 .text.HAL_RTC_GetState
                0x00000000       0x1c objs/stm32l4xx_hal_rtc.o
 .text.RTC_EnterInitMode
                0x00000000       0x66 objs/stm32l4xx_hal_rtc.o
 .text.RTC_ExitInitMode
                0x00000000       0x7c objs/stm32l4xx_hal_rtc.o
 .text.RTC_ByteToBcd2
                0x00000000       0x40 objs/stm32l4xx_hal_rtc.o
 .text          0x00000000        0x0 objs/stm32l4xx_hal_rtc_ex.o
 .data          0x00000000        0x0 objs/stm32l4xx_hal_rtc_ex.o
 .bss           0x00000000        0x0 objs/stm32l4xx_hal_rtc_ex.o
 .text.HAL_RTCEx_SetTimeStamp
                0x00000000       0x90 objs/stm32l4xx_hal_rtc_ex.o
 .text.HAL_RTCEx_SetTimeStamp_IT
                0x00000000       0xbc objs/stm32l4xx_hal_rtc_ex.o
 .text.HAL_RTCEx_DeactivateTimeStamp
                0x00000000       0x84 objs/stm32l4xx_hal_rtc_ex.o
 .text.HAL_RTCEx_SetInternalTimeStamp
                0x00000000       0x6c objs/stm32l4xx_hal_rtc_ex.o
 .text.HAL_RTCEx_DeactivateInternalTimeStamp
                0x00000000       0x6c objs/stm32l4xx_hal_rtc_ex.o
 .text.HAL_RTCEx_GetTimeStamp
                0x00000000      0x150 objs/stm32l4xx_hal_rtc_ex.o
 .text.HAL_RTCEx_TamperTimeStampIRQHandler
                0x00000000      0x104 objs/stm32l4xx_hal_rtc_ex.o
 .text.HAL_RTCEx_TimeStampEventCallback
                0x00000000       0x14 objs/stm32l4xx_hal_rtc_ex.o
 .text.HAL_RTCEx_PollForTimeStampEvent
                0x00000000       0x88 objs/stm32l4xx_hal_rtc_ex.o
 .text.HAL_RTCEx_SetWakeUpTimer
                0x00000000       0xe8 objs/stm32l4xx_hal_rtc_ex.o
 .text.HAL_RTCEx_SetWakeUpTimer_IT
                0x00000000      0x128 objs/stm32l4xx_hal_rtc_ex.o
 .text.HAL_RTCEx_DeactivateWakeUpTimer
                0x00000000       0xba objs/stm32l4xx_hal_rtc_ex.o
 .text.HAL_RTCEx_GetWakeUpTimer
                0x00000000       0x1c objs/stm32l4xx_hal_rtc_ex.o
 .text.HAL_RTCEx_WakeUpTimerIRQHandler
                0x00000000       0x4c objs/stm32l4xx_hal_rtc_ex.o
 .text.HAL_RTCEx_WakeUpTimerEventCallback
                0x00000000       0x14 objs/stm32l4xx_hal_rtc_ex.o
 .text.HAL_RTCEx_PollForWakeUpTimerEvent
                0x00000000       0x6e objs/stm32l4xx_hal_rtc_ex.o
 .text.HAL_RTCEx_SetSmoothCalib
                0x00000000       0xce objs/stm32l4xx_hal_rtc_ex.o
 .text.HAL_RTCEx_SetSynchroShift
                0x00000000      0x10a objs/stm32l4xx_hal_rtc_ex.o
 .text.HAL_RTCEx_SetCalibrationOutPut
                0x00000000       0x8e objs/stm32l4xx_hal_rtc_ex.o
 .text.HAL_RTCEx_DeactivateCalibrationOutPut
                0x00000000       0x6c objs/stm32l4xx_hal_rtc_ex.o
 .text.HAL_RTCEx_SetRefClock
                0x00000000       0x88 objs/stm32l4xx_hal_rtc_ex.o
 .text.HAL_RTCEx_DeactivateRefClock
                0x00000000       0x88 objs/stm32l4xx_hal_rtc_ex.o
 .text.HAL_RTCEx_EnableBypassShadow
                0x00000000       0x6c objs/stm32l4xx_hal_rtc_ex.o
 .text.HAL_RTCEx_DisableBypassShadow
                0x00000000       0x6c objs/stm32l4xx_hal_rtc_ex.o
 .text.HAL_RTCEx_AlarmBEventCallback
                0x00000000       0x14 objs/stm32l4xx_hal_rtc_ex.o
 .text.HAL_RTCEx_PollForAlarmBEvent
                0x00000000       0x6e objs/stm32l4xx_hal_rtc_ex.o
 .text.HAL_RTCEx_SetTamper
                0x00000000      0x178 objs/stm32l4xx_hal_rtc_ex.o
 .text.HAL_RTCEx_SetTamper_IT
                0x00000000      0x1a8 objs/stm32l4xx_hal_rtc_ex.o
 .text.HAL_RTCEx_DeactivateTamper
                0x00000000       0xb2 objs/stm32l4xx_hal_rtc_ex.o
 .text.HAL_RTCEx_PollForTamper1Event
                0x00000000       0x6e objs/stm32l4xx_hal_rtc_ex.o
 .text.HAL_RTCEx_PollForTamper2Event
                0x00000000       0x6e objs/stm32l4xx_hal_rtc_ex.o
 .text.HAL_RTCEx_PollForTamper3Event
                0x00000000       0x74 objs/stm32l4xx_hal_rtc_ex.o
 .text.HAL_RTCEx_Tamper1EventCallback
                0x00000000       0x14 objs/stm32l4xx_hal_rtc_ex.o
 .text.HAL_RTCEx_Tamper2EventCallback
                0x00000000       0x14 objs/stm32l4xx_hal_rtc_ex.o
 .text.HAL_RTCEx_Tamper3EventCallback
                0x00000000       0x14 objs/stm32l4xx_hal_rtc_ex.o
 .text          0x00000000        0x0 objs/stm32l4xx_hal_rcc.o
 .data          0x00000000        0x0 objs/stm32l4xx_hal_rcc.o
 .bss           0x00000000        0x0 objs/stm32l4xx_hal_rcc.o
 .text.HAL_RCC_DeInit
                0x00000000      0x130 objs/stm32l4xx_hal_rcc.o
 .text.HAL_RCC_OscConfig
                0x00000000      0x840 objs/stm32l4xx_hal_rcc.o
 .text.HAL_RCC_ClockConfig
                0x00000000      0x1c4 objs/stm32l4xx_hal_rcc.o
 .text.HAL_RCC_MCOConfig
                0x00000000       0x68 objs/stm32l4xx_hal_rcc.o
 .text.HAL_RCC_GetOscConfig
                0x00000000      0x194 objs/stm32l4xx_hal_rcc.o
 .text.HAL_RCC_GetClockConfig
                0x00000000       0x64 objs/stm32l4xx_hal_rcc.o
 .text.HAL_RCC_EnableCSS
                0x00000000       0x20 objs/stm32l4xx_hal_rcc.o
 .text.HAL_RCC_NMI_IRQHandler
                0x00000000       0x28 objs/stm32l4xx_hal_rcc.o
 .text.HAL_RCC_CSSCallback
                0x00000000        0xe objs/stm32l4xx_hal_rcc.o
 .text.RCC_SetFlashLatencyFromMSIRange
                0x00000000       0xc0 objs/stm32l4xx_hal_rcc.o
 .text          0x00000000        0x0 objs/stm32l4xx_hal_rcc_ex.o
 .data          0x00000000        0x0 objs/stm32l4xx_hal_rcc_ex.o
 .bss           0x00000000        0x0 objs/stm32l4xx_hal_rcc_ex.o
 .text.HAL_RCCEx_GetPeriphCLKConfig
                0x00000000      0x154 objs/stm32l4xx_hal_rcc_ex.o
 .text.HAL_RCCEx_GetPeriphCLKFreq
                0x00000000      0x92c objs/stm32l4xx_hal_rcc_ex.o
 .text.HAL_RCCEx_EnablePLLSAI1
                0x00000000       0xd0 objs/stm32l4xx_hal_rcc_ex.o
 .text.HAL_RCCEx_DisablePLLSAI1
                0x00000000       0x74 objs/stm32l4xx_hal_rcc_ex.o
 .text.HAL_RCCEx_WakeUpStopCLKConfig
                0x00000000       0x28 objs/stm32l4xx_hal_rcc_ex.o
 .text.HAL_RCCEx_StandbyMSIRangeConfig
                0x00000000       0x30 objs/stm32l4xx_hal_rcc_ex.o
 .text.HAL_RCCEx_EnableLSECSS
                0x00000000       0x24 objs/stm32l4xx_hal_rcc_ex.o
 .text.HAL_RCCEx_DisableLSECSS
                0x00000000       0x30 objs/stm32l4xx_hal_rcc_ex.o
 .text.HAL_RCCEx_EnableLSECSS_IT
                0x00000000       0x4c objs/stm32l4xx_hal_rcc_ex.o
 .text.HAL_RCCEx_LSECSS_IRQHandler
                0x00000000       0x28 objs/stm32l4xx_hal_rcc_ex.o
 .text.HAL_RCCEx_LSECSS_Callback
                0x00000000        0xe objs/stm32l4xx_hal_rcc_ex.o
 .text.HAL_RCCEx_EnableLSCO
                0x00000000       0xd4 objs/stm32l4xx_hal_rcc_ex.o
 .text.HAL_RCCEx_DisableLSCO
                0x00000000       0x88 objs/stm32l4xx_hal_rcc_ex.o
 .text.HAL_RCCEx_EnableMSIPLLMode
                0x00000000       0x20 objs/stm32l4xx_hal_rcc_ex.o
 .text.HAL_RCCEx_DisableMSIPLLMode
                0x00000000       0x20 objs/stm32l4xx_hal_rcc_ex.o
 .text.HAL_RCCEx_CRSConfig
                0x00000000       0x84 objs/stm32l4xx_hal_rcc_ex.o
 .text.HAL_RCCEx_CRSSoftwareSynchronizationGenerate
                0x00000000       0x20 objs/stm32l4xx_hal_rcc_ex.o
 .text.HAL_RCCEx_CRSGetSynchronizationInfo
                0x00000000       0x48 objs/stm32l4xx_hal_rcc_ex.o
 .text.HAL_RCCEx_CRSWaitSynchronization
                0x00000000       0xe4 objs/stm32l4xx_hal_rcc_ex.o
 .text.HAL_RCCEx_CRS_IRQHandler
                0x00000000       0xdc objs/stm32l4xx_hal_rcc_ex.o
 .text.HAL_RCCEx_CRS_SyncOkCallback
                0x00000000        0xe objs/stm32l4xx_hal_rcc_ex.o
 .text.HAL_RCCEx_CRS_SyncWarnCallback
                0x00000000        0xe objs/stm32l4xx_hal_rcc_ex.o
 .text.HAL_RCCEx_CRS_ExpectedSyncCallback
                0x00000000        0xe objs/stm32l4xx_hal_rcc_ex.o
 .text.HAL_RCCEx_CRS_ErrorCallback
                0x00000000       0x14 objs/stm32l4xx_hal_rcc_ex.o
 .text.RCCEx_GetSAIxPeriphCLKFreq
                0x00000000      0x178 objs/stm32l4xx_hal_rcc_ex.o
 .text          0x00000000        0x0 objs/stm32l4xx_hal_uart.o
 .data          0x00000000        0x0 objs/stm32l4xx_hal_uart.o
 .bss           0x00000000        0x0 objs/stm32l4xx_hal_uart.o
 .text.HAL_HalfDuplex_Init
                0x00000000       0xac objs/stm32l4xx_hal_uart.o
 .text.HAL_LIN_Init
                0x00000000       0xdc objs/stm32l4xx_hal_uart.o
 .text.HAL_MultiProcessor_Init
                0x00000000       0xd4 objs/stm32l4xx_hal_uart.o
 .text.HAL_UART_DeInit
                0x00000000       0x72 objs/stm32l4xx_hal_uart.o
 .text.HAL_UART_MspDeInit
                0x00000000       0x14 objs/stm32l4xx_hal_uart.o
 .text.HAL_UART_Receive
                0x00000000      0x1a2 objs/stm32l4xx_hal_uart.o
 .text.HAL_UART_Transmit_IT
                0x00000000       0xb8 objs/stm32l4xx_hal_uart.o
 .text.HAL_UART_Receive_IT
                0x00000000       0x88 objs/stm32l4xx_hal_uart.o
 .text.HAL_UART_Transmit_DMA
                0x00000000       0xfc objs/stm32l4xx_hal_uart.o
 .text.HAL_UART_Receive_DMA
                0x00000000       0x88 objs/stm32l4xx_hal_uart.o
 .text.HAL_UART_DMAPause
                0x00000000       0xa8 objs/stm32l4xx_hal_uart.o
 .text.HAL_UART_DMAResume
                0x00000000       0x8c objs/stm32l4xx_hal_uart.o
 .text.HAL_UART_DMAStop
                0x00000000       0xda objs/stm32l4xx_hal_uart.o
 .text.HAL_UART_Abort
                0x00000000      0x144 objs/stm32l4xx_hal_uart.o
 .text.HAL_UART_AbortTransmit
                0x00000000       0x8a objs/stm32l4xx_hal_uart.o
 .text.HAL_UART_AbortReceive
                0x00000000       0xd4 objs/stm32l4xx_hal_uart.o
 .text.HAL_UART_Abort_IT
                0x00000000      0x190 objs/stm32l4xx_hal_uart.o
 .text.HAL_UART_AbortTransmit_IT
                0x00000000       0xac objs/stm32l4xx_hal_uart.o
 .text.HAL_UART_AbortReceive_IT
                0x00000000      0x104 objs/stm32l4xx_hal_uart.o
 .text.HAL_UART_IRQHandler
                0x00000000      0x404 objs/stm32l4xx_hal_uart.o
 .text.HAL_UART_TxCpltCallback
                0x00000000       0x14 objs/stm32l4xx_hal_uart.o
 .text.HAL_UART_TxHalfCpltCallback
                0x00000000       0x14 objs/stm32l4xx_hal_uart.o
 .text.HAL_UART_RxCpltCallback
                0x00000000       0x14 objs/stm32l4xx_hal_uart.o
 .text.HAL_UART_RxHalfCpltCallback
                0x00000000       0x14 objs/stm32l4xx_hal_uart.o
 .text.HAL_UART_ErrorCallback
                0x00000000       0x14 objs/stm32l4xx_hal_uart.o
 .text.HAL_UART_AbortCpltCallback
                0x00000000       0x14 objs/stm32l4xx_hal_uart.o
 .text.HAL_UART_AbortTransmitCpltCallback
                0x00000000       0x14 objs/stm32l4xx_hal_uart.o
 .text.HAL_UART_AbortReceiveCpltCallback
                0x00000000       0x14 objs/stm32l4xx_hal_uart.o
 .text.HAL_UARTEx_RxEventCallback
                0x00000000       0x18 objs/stm32l4xx_hal_uart.o
 .text.HAL_UART_ReceiverTimeout_Config
                0x00000000       0x38 objs/stm32l4xx_hal_uart.o
 .text.HAL_UART_EnableReceiverTimeout
                0x00000000       0x70 objs/stm32l4xx_hal_uart.o
 .text.HAL_UART_DisableReceiverTimeout
                0x00000000       0x70 objs/stm32l4xx_hal_uart.o
 .text.HAL_MultiProcessor_EnableMuteMode
                0x00000000       0x4a objs/stm32l4xx_hal_uart.o
 .text.HAL_MultiProcessor_DisableMuteMode
                0x00000000       0x4a objs/stm32l4xx_hal_uart.o
 .text.HAL_MultiProcessor_EnterMuteMode
                0x00000000       0x28 objs/stm32l4xx_hal_uart.o
 .text.HAL_HalfDuplex_EnableTransmitter
                0x00000000       0x60 objs/stm32l4xx_hal_uart.o
 .text.HAL_HalfDuplex_EnableReceiver
                0x00000000       0x60 objs/stm32l4xx_hal_uart.o
 .text.HAL_LIN_SendBreak
                0x00000000       0x54 objs/stm32l4xx_hal_uart.o
 .text.HAL_UART_GetState
                0x00000000       0x26 objs/stm32l4xx_hal_uart.o
 .text.HAL_UART_GetError
                0x00000000       0x1a objs/stm32l4xx_hal_uart.o
 .text.UART_Start_Receive_IT
                0x00000000      0x110 objs/stm32l4xx_hal_uart.o
 .text.UART_Start_Receive_DMA
                0x00000000       0xd8 objs/stm32l4xx_hal_uart.o
 .text.UART_EndTxTransfer
                0x00000000       0x2a objs/stm32l4xx_hal_uart.o
 .text.UART_EndRxTransfer
                0x00000000       0x5e objs/stm32l4xx_hal_uart.o
 .text.UART_DMATransmitCplt
                0x00000000       0x54 objs/stm32l4xx_hal_uart.o
 .text.UART_DMATxHalfCplt
                0x00000000       0x1c objs/stm32l4xx_hal_uart.o
 .text.UART_DMAReceiveCplt
                0x00000000       0x98 objs/stm32l4xx_hal_uart.o
 .text.UART_DMARxHalfCplt
                0x00000000       0x38 objs/stm32l4xx_hal_uart.o
 .text.UART_DMAError
                0x00000000       0x7c objs/stm32l4xx_hal_uart.o
 .text.UART_DMAAbortOnError
                0x00000000       0x2c objs/stm32l4xx_hal_uart.o
 .text.UART_DMATxAbortCallback
                0x00000000       0x6a objs/stm32l4xx_hal_uart.o
 .text.UART_DMARxAbortCallback
                0x00000000       0x7e objs/stm32l4xx_hal_uart.o
 .text.UART_DMATxOnlyAbortCallback
                0x00000000       0x2a objs/stm32l4xx_hal_uart.o
 .text.UART_DMARxOnlyAbortCallback
                0x00000000       0x4c objs/stm32l4xx_hal_uart.o
 .text.UART_TxISR_8BIT
                0x00000000       0x72 objs/stm32l4xx_hal_uart.o
 .text.UART_TxISR_16BIT
                0x00000000       0x7c objs/stm32l4xx_hal_uart.o
 .text.UART_EndTransmit_IT
                0x00000000       0x32 objs/stm32l4xx_hal_uart.o
 .text.UART_RxISR_8BIT
                0x00000000       0xd8 objs/stm32l4xx_hal_uart.o
 .text.UART_RxISR_16BIT
                0x00000000       0xd8 objs/stm32l4xx_hal_uart.o
 .text          0x00000000        0x0 objs/stm32l4xx_hal_uart_ex.o
 .data          0x00000000        0x0 objs/stm32l4xx_hal_uart_ex.o
 .bss           0x00000000        0x0 objs/stm32l4xx_hal_uart_ex.o
 .text.HAL_RS485Ex_Init
                0x00000000       0xce objs/stm32l4xx_hal_uart_ex.o
 .text.HAL_UARTEx_WakeupCallback
                0x00000000       0x14 objs/stm32l4xx_hal_uart_ex.o
 .text.HAL_UARTEx_EnableClockStopMode
                0x00000000       0x44 objs/stm32l4xx_hal_uart_ex.o
 .text.HAL_MultiProcessorEx_AddressLength_Set
                0x00000000       0x5e objs/stm32l4xx_hal_uart_ex.o
 .text.HAL_UARTEx_StopModeWakeUpSourceConfig
                0x00000000       0xb2 objs/stm32l4xx_hal_uart_ex.o
 .text.HAL_UARTEx_ReceiveToIdle
                0x00000000      0x20e objs/stm32l4xx_hal_uart_ex.o
 .text.HAL_UARTEx_ReceiveToIdle_IT
                0x00000000       0x8c objs/stm32l4xx_hal_uart_ex.o
 .text.HAL_UARTEx_ReceiveToIdle_DMA
                0x00000000       0x8c objs/stm32l4xx_hal_uart_ex.o
 .text.UARTEx_Wakeup_AddressConfig
                0x00000000       0x46 objs/stm32l4xx_hal_uart_ex.o
 .text          0x00000000        0x0 objs/stm32l4xx_hal_i2c.o
 .data          0x00000000        0x0 objs/stm32l4xx_hal_i2c.o
 .bss           0x00000000        0x0 objs/stm32l4xx_hal_i2c.o
 .text.HAL_I2C_Init
                0x00000000      0x11e objs/stm32l4xx_hal_i2c.o
 .text.HAL_I2C_DeInit
                0x00000000       0x5e objs/stm32l4xx_hal_i2c.o
 .text.HAL_I2C_MspInit
                0x00000000       0x14 objs/stm32l4xx_hal_i2c.o
 .text.HAL_I2C_MspDeInit
                0x00000000       0x14 objs/stm32l4xx_hal_i2c.o
 .text.HAL_I2C_Master_Transmit
                0x00000000      0x1e8 objs/stm32l4xx_hal_i2c.o
 .text.HAL_I2C_Master_Receive
                0x00000000      0x1ec objs/stm32l4xx_hal_i2c.o
 .text.HAL_I2C_Slave_Transmit
                0x00000000      0x212 objs/stm32l4xx_hal_i2c.o
 .text.HAL_I2C_Slave_Receive
                0x00000000      0x1fe objs/stm32l4xx_hal_i2c.o
 .text.HAL_I2C_Master_Transmit_IT
                0x00000000       0xe0 objs/stm32l4xx_hal_i2c.o
 .text.HAL_I2C_Master_Receive_IT
                0x00000000       0xe0 objs/stm32l4xx_hal_i2c.o
 .text.HAL_I2C_Slave_Transmit_IT
                0x00000000       0xa0 objs/stm32l4xx_hal_i2c.o
 .text.HAL_I2C_Slave_Receive_IT
                0x00000000       0xa0 objs/stm32l4xx_hal_i2c.o
 .text.HAL_I2C_Master_Transmit_DMA
                0x00000000      0x1e0 objs/stm32l4xx_hal_i2c.o
 .text.HAL_I2C_Master_Receive_DMA
                0x00000000      0x1e0 objs/stm32l4xx_hal_i2c.o
 .text.HAL_I2C_Slave_Transmit_DMA
                0x00000000      0x16c objs/stm32l4xx_hal_i2c.o
 .text.HAL_I2C_Slave_Receive_DMA
                0x00000000      0x16c objs/stm32l4xx_hal_i2c.o
 .text.HAL_I2C_Mem_Write
                0x00000000      0x228 objs/stm32l4xx_hal_i2c.o
 .text.HAL_I2C_Mem_Read
                0x00000000      0x234 objs/stm32l4xx_hal_i2c.o
 .text.HAL_I2C_Mem_Write_IT
                0x00000000      0x128 objs/stm32l4xx_hal_i2c.o
 .text.HAL_I2C_Mem_Read_IT
                0x00000000      0x12c objs/stm32l4xx_hal_i2c.o
 .text.HAL_I2C_Mem_Write_DMA
                0x00000000      0x1ec objs/stm32l4xx_hal_i2c.o
 .text.HAL_I2C_Mem_Read_DMA
                0x00000000      0x1f0 objs/stm32l4xx_hal_i2c.o
 .text.HAL_I2C_IsDeviceReady
                0x00000000      0x20e objs/stm32l4xx_hal_i2c.o
 .text.HAL_I2C_Master_Seq_Transmit_IT
                0x00000000      0x108 objs/stm32l4xx_hal_i2c.o
 .text.HAL_I2C_Master_Seq_Transmit_DMA
                0x00000000      0x208 objs/stm32l4xx_hal_i2c.o
 .text.HAL_I2C_Master_Seq_Receive_IT
                0x00000000      0x108 objs/stm32l4xx_hal_i2c.o
 .text.HAL_I2C_Master_Seq_Receive_DMA
                0x00000000      0x208 objs/stm32l4xx_hal_i2c.o
 .text.HAL_I2C_Slave_Seq_Transmit_IT
                0x00000000      0x148 objs/stm32l4xx_hal_i2c.o
 .text.HAL_I2C_Slave_Seq_Transmit_DMA
                0x00000000      0x270 objs/stm32l4xx_hal_i2c.o
 .text.HAL_I2C_Slave_Seq_Receive_IT
                0x00000000      0x148 objs/stm32l4xx_hal_i2c.o
 .text.HAL_I2C_Slave_Seq_Receive_DMA
                0x00000000      0x270 objs/stm32l4xx_hal_i2c.o
 .text.HAL_I2C_EnableListen_IT
                0x00000000       0x40 objs/stm32l4xx_hal_i2c.o
 .text.HAL_I2C_DisableListen_IT
                0x00000000       0x62 objs/stm32l4xx_hal_i2c.o
 .text.HAL_I2C_Master_Abort_IT
                0x00000000       0xa0 objs/stm32l4xx_hal_i2c.o
 .text.HAL_I2C_EV_IRQHandler
                0x00000000       0x34 objs/stm32l4xx_hal_i2c.o
 .text.HAL_I2C_ER_IRQHandler
                0x00000000       0xc2 objs/stm32l4xx_hal_i2c.o
 .text.HAL_I2C_MasterTxCpltCallback
                0x00000000       0x14 objs/stm32l4xx_hal_i2c.o
 .text.HAL_I2C_MasterRxCpltCallback
                0x00000000       0x14 objs/stm32l4xx_hal_i2c.o
 .text.HAL_I2C_SlaveTxCpltCallback
                0x00000000       0x14 objs/stm32l4xx_hal_i2c.o
 .text.HAL_I2C_SlaveRxCpltCallback
                0x00000000       0x14 objs/stm32l4xx_hal_i2c.o
 .text.HAL_I2C_AddrCallback
                0x00000000       0x1c objs/stm32l4xx_hal_i2c.o
 .text.HAL_I2C_ListenCpltCallback
                0x00000000       0x14 objs/stm32l4xx_hal_i2c.o
 .text.HAL_I2C_MemTxCpltCallback
                0x00000000       0x14 objs/stm32l4xx_hal_i2c.o
 .text.HAL_I2C_MemRxCpltCallback
                0x00000000       0x14 objs/stm32l4xx_hal_i2c.o
 .text.HAL_I2C_ErrorCallback
                0x00000000       0x14 objs/stm32l4xx_hal_i2c.o
 .text.HAL_I2C_AbortCpltCallback
                0x00000000       0x14 objs/stm32l4xx_hal_i2c.o
 .text.HAL_I2C_GetState
                0x00000000       0x1c objs/stm32l4xx_hal_i2c.o
 .text.HAL_I2C_GetMode
                0x00000000       0x1c objs/stm32l4xx_hal_i2c.o
 .text.HAL_I2C_GetError
                0x00000000       0x18 objs/stm32l4xx_hal_i2c.o
 .text.I2C_Master_ISR_IT
                0x00000000      0x250 objs/stm32l4xx_hal_i2c.o
 .text.I2C_Slave_ISR_IT
                0x00000000      0x206 objs/stm32l4xx_hal_i2c.o
 .text.I2C_Master_ISR_DMA
                0x00000000      0x1e6 objs/stm32l4xx_hal_i2c.o
 .text.I2C_Slave_ISR_DMA
                0x00000000      0x1c0 objs/stm32l4xx_hal_i2c.o
 .text.I2C_RequestMemoryWrite
                0x00000000       0xa8 objs/stm32l4xx_hal_i2c.o
 .text.I2C_RequestMemoryRead
                0x00000000       0xa8 objs/stm32l4xx_hal_i2c.o
 .text.I2C_ITAddrCplt
                0x00000000      0x108 objs/stm32l4xx_hal_i2c.o
 .text.I2C_ITMasterSeqCplt
                0x00000000       0x7a objs/stm32l4xx_hal_i2c.o
 .text.I2C_ITSlaveSeqCplt
                0x00000000       0xbc objs/stm32l4xx_hal_i2c.o
 .text.I2C_ITMasterCplt
                0x00000000      0x194 objs/stm32l4xx_hal_i2c.o
 .text.I2C_ITSlaveCplt
                0x00000000      0x214 objs/stm32l4xx_hal_i2c.o
 .text.I2C_ITListenCplt
                0x00000000       0xac objs/stm32l4xx_hal_i2c.o
 .text.I2C_ITError
                0x00000000      0x1a0 objs/stm32l4xx_hal_i2c.o
 .text.I2C_TreatErrorCallback
                0x00000000       0x4e objs/stm32l4xx_hal_i2c.o
 .text.I2C_Flush_TXDR
                0x00000000       0x48 objs/stm32l4xx_hal_i2c.o
 .text.I2C_DMAMasterTransmitCplt
                0x00000000       0x96 objs/stm32l4xx_hal_i2c.o
 .text.I2C_DMASlaveTransmitCplt
                0x00000000       0x40 objs/stm32l4xx_hal_i2c.o
 .text.I2C_DMAMasterReceiveCplt
                0x00000000       0x96 objs/stm32l4xx_hal_i2c.o
 .text.I2C_DMASlaveReceiveCplt
                0x00000000       0x46 objs/stm32l4xx_hal_i2c.o
 .text.I2C_DMAError
                0x00000000       0x2e objs/stm32l4xx_hal_i2c.o
 .text.I2C_DMAAbort
                0x00000000       0x3c objs/stm32l4xx_hal_i2c.o
 .text.I2C_WaitOnFlagUntilTimeout
                0x00000000       0x80 objs/stm32l4xx_hal_i2c.o
 .text.I2C_WaitOnTXISFlagUntilTimeout
                0x00000000       0x80 objs/stm32l4xx_hal_i2c.o
 .text.I2C_WaitOnSTOPFlagUntilTimeout
                0x00000000       0x78 objs/stm32l4xx_hal_i2c.o
 .text.I2C_WaitOnRXNEFlagUntilTimeout
                0x00000000       0xd8 objs/stm32l4xx_hal_i2c.o
 .text.I2C_IsAcknowledgeFailed
                0x00000000       0xcc objs/stm32l4xx_hal_i2c.o
 .text.I2C_TransferConfig
                0x00000000       0x5c objs/stm32l4xx_hal_i2c.o
 .text.I2C_Enable_IRQ
                0x00000000       0xc8 objs/stm32l4xx_hal_i2c.o
 .text.I2C_Disable_IRQ
                0x00000000       0xbc objs/stm32l4xx_hal_i2c.o
 .text.I2C_ConvertOtherXferOptions
                0x00000000       0x36 objs/stm32l4xx_hal_i2c.o
 .debug_info    0x00000000     0x1f1d objs/stm32l4xx_hal_i2c.o
 .debug_abbrev  0x00000000      0x257 objs/stm32l4xx_hal_i2c.o
 .debug_aranges
                0x00000000      0x290 objs/stm32l4xx_hal_i2c.o
 .debug_rnglists
                0x00000000      0x219 objs/stm32l4xx_hal_i2c.o
 .debug_line    0x00000000     0x2b01 objs/stm32l4xx_hal_i2c.o
 .debug_str     0x00000000      0xf68 objs/stm32l4xx_hal_i2c.o
 .comment       0x00000000       0x27 objs/stm32l4xx_hal_i2c.o
 .debug_frame   0x00000000      0xbe8 objs/stm32l4xx_hal_i2c.o
 .ARM.attributes
                0x00000000       0x34 objs/stm32l4xx_hal_i2c.o
 .text          0x00000000        0x0 objs/stm32l4xx_hal_i2c_ex.o
 .data          0x00000000        0x0 objs/stm32l4xx_hal_i2c_ex.o
 .bss           0x00000000        0x0 objs/stm32l4xx_hal_i2c_ex.o
 .text.HAL_I2CEx_ConfigAnalogFilter
                0x00000000       0x96 objs/stm32l4xx_hal_i2c_ex.o
 .text.HAL_I2CEx_ConfigDigitalFilter
                0x00000000       0x98 objs/stm32l4xx_hal_i2c_ex.o
 .text.HAL_I2CEx_EnableWakeUp
                0x00000000       0x84 objs/stm32l4xx_hal_i2c_ex.o
 .text.HAL_I2CEx_DisableWakeUp
                0x00000000       0x84 objs/stm32l4xx_hal_i2c_ex.o
 .text.HAL_I2CEx_EnableFastModePlus
                0x00000000       0x40 objs/stm32l4xx_hal_i2c_ex.o
 .text.HAL_I2CEx_DisableFastModePlus
                0x00000000       0x44 objs/stm32l4xx_hal_i2c_ex.o
 .debug_info    0x00000000      0x944 objs/stm32l4xx_hal_i2c_ex.o
 .debug_abbrev  0x00000000      0x1c1 objs/stm32l4xx_hal_i2c_ex.o
 .debug_aranges
                0x00000000       0x48 objs/stm32l4xx_hal_i2c_ex.o
 .debug_rnglists
                0x00000000       0x35 objs/stm32l4xx_hal_i2c_ex.o
 .debug_line    0x00000000      0x262 objs/stm32l4xx_hal_i2c_ex.o
 .debug_str     0x00000000      0x8dd objs/stm32l4xx_hal_i2c_ex.o
 .comment       0x00000000       0x27 objs/stm32l4xx_hal_i2c_ex.o
 .debug_frame   0x00000000      0x100 objs/stm32l4xx_hal_i2c_ex.o
 .ARM.attributes
                0x00000000       0x34 objs/stm32l4xx_hal_i2c_ex.o
 .text          0x00000000        0x0 objs/stm32l4xx_hal_adc.o
 .data          0x00000000        0x0 objs/stm32l4xx_hal_adc.o
 .bss           0x00000000        0x0 objs/stm32l4xx_hal_adc.o
 .text.LL_ADC_SetCommonClock
                0x00000000       0x26 objs/stm32l4xx_hal_adc.o
 .text.LL_ADC_SetCommonPathInternalCh
                0x00000000       0x26 objs/stm32l4xx_hal_adc.o
 .text.LL_ADC_GetCommonPathInternalCh
                0x00000000       0x1c objs/stm32l4xx_hal_adc.o
 .text.LL_ADC_SetOffset
                0x00000000       0x48 objs/stm32l4xx_hal_adc.o
 .text.LL_ADC_GetOffsetChannel
                0x00000000       0x2c objs/stm32l4xx_hal_adc.o
 .text.LL_ADC_SetOffsetState
                0x00000000       0x36 objs/stm32l4xx_hal_adc.o
 .text.LL_ADC_REG_IsTriggerSourceSWStart
                0x00000000       0x26 objs/stm32l4xx_hal_adc.o
 .text.LL_ADC_REG_SetSequencerRanks
                0x00000000       0x58 objs/stm32l4xx_hal_adc.o
 .text.LL_ADC_INJ_IsTriggerSourceSWStart
                0x00000000       0x26 objs/stm32l4xx_hal_adc.o
 .text.LL_ADC_SetChannelSamplingTime
                0x00000000       0x56 objs/stm32l4xx_hal_adc.o
 .text.LL_ADC_SetChannelSingleDiff
                0x00000000       0x48 objs/stm32l4xx_hal_adc.o
 .text.LL_ADC_SetAnalogWDMonitChannels
                0x00000000       0x5a objs/stm32l4xx_hal_adc.o
 .text.LL_ADC_ConfigAnalogWDThresholds
                0x00000000       0x44 objs/stm32l4xx_hal_adc.o
 .text.LL_ADC_DisableDeepPowerDown
                0x00000000       0x24 objs/stm32l4xx_hal_adc.o
 .text.LL_ADC_IsDeepPowerDownEnabled
                0x00000000       0x28 objs/stm32l4xx_hal_adc.o
 .text.LL_ADC_EnableInternalRegulator
                0x00000000       0x28 objs/stm32l4xx_hal_adc.o
 .text.LL_ADC_IsInternalRegulatorEnabled
                0x00000000       0x28 objs/stm32l4xx_hal_adc.o
 .text.LL_ADC_Enable
                0x00000000       0x28 objs/stm32l4xx_hal_adc.o
 .text.LL_ADC_Disable
                0x00000000       0x28 objs/stm32l4xx_hal_adc.o
 .text.LL_ADC_IsEnabled
                0x00000000       0x26 objs/stm32l4xx_hal_adc.o
 .text.LL_ADC_IsDisableOngoing
                0x00000000       0x26 objs/stm32l4xx_hal_adc.o
 .text.LL_ADC_REG_StartConversion
                0x00000000       0x28 objs/stm32l4xx_hal_adc.o
 .text.LL_ADC_REG_StopConversion
                0x00000000       0x28 objs/stm32l4xx_hal_adc.o
 .text.LL_ADC_REG_IsConversionOngoing
                0x00000000       0x26 objs/stm32l4xx_hal_adc.o
 .text.LL_ADC_INJ_StopConversion
                0x00000000       0x28 objs/stm32l4xx_hal_adc.o
 .text.LL_ADC_INJ_IsConversionOngoing
                0x00000000       0x26 objs/stm32l4xx_hal_adc.o
 .text.LL_ADC_ClearFlag_AWD1
                0x00000000       0x1a objs/stm32l4xx_hal_adc.o
 .text.LL_ADC_ClearFlag_AWD2
                0x00000000       0x1c objs/stm32l4xx_hal_adc.o
 .text.LL_ADC_ClearFlag_AWD3
                0x00000000       0x1c objs/stm32l4xx_hal_adc.o
 .text.LL_ADC_EnableIT_AWD1
                0x00000000       0x20 objs/stm32l4xx_hal_adc.o
 .text.LL_ADC_EnableIT_AWD2
                0x00000000       0x20 objs/stm32l4xx_hal_adc.o
 .text.LL_ADC_EnableIT_AWD3
                0x00000000       0x20 objs/stm32l4xx_hal_adc.o
 .text.LL_ADC_DisableIT_AWD1
                0x00000000       0x20 objs/stm32l4xx_hal_adc.o
 .text.LL_ADC_DisableIT_AWD2
                0x00000000       0x20 objs/stm32l4xx_hal_adc.o
 .text.LL_ADC_DisableIT_AWD3
                0x00000000       0x20 objs/stm32l4xx_hal_adc.o
 .text.HAL_ADC_Init
                0x00000000      0x290 objs/stm32l4xx_hal_adc.o
 .text.HAL_ADC_DeInit
                0x00000000      0x250 objs/stm32l4xx_hal_adc.o
 .text.HAL_ADC_MspInit
                0x00000000       0x14 objs/stm32l4xx_hal_adc.o
 .text.HAL_ADC_MspDeInit
                0x00000000       0x14 objs/stm32l4xx_hal_adc.o
 .text.HAL_ADC_Start
                0x00000000       0xc6 objs/stm32l4xx_hal_adc.o
 .text.HAL_ADC_Stop
                0x00000000       0x66 objs/stm32l4xx_hal_adc.o
 .text.HAL_ADC_PollForConversion
                0x00000000      0x11e objs/stm32l4xx_hal_adc.o
 .text.HAL_ADC_PollForEvent
                0x00000000      0x15e objs/stm32l4xx_hal_adc.o
 .text.HAL_ADC_Start_IT
                0x00000000      0x168 objs/stm32l4xx_hal_adc.o
 .text.HAL_ADC_Stop_IT
                0x00000000       0x76 objs/stm32l4xx_hal_adc.o
 .text.HAL_ADC_Start_DMA
                0x00000000      0x108 objs/stm32l4xx_hal_adc.o
 .text.HAL_ADC_Stop_DMA
                0x00000000       0xc2 objs/stm32l4xx_hal_adc.o
 .text.HAL_ADC_GetValue
                0x00000000       0x1a objs/stm32l4xx_hal_adc.o
 .text.HAL_ADC_IRQHandler
                0x00000000      0x368 objs/stm32l4xx_hal_adc.o
 .text.HAL_ADC_ConvCpltCallback
                0x00000000       0x14 objs/stm32l4xx_hal_adc.o
 .text.HAL_ADC_ConvHalfCpltCallback
                0x00000000       0x14 objs/stm32l4xx_hal_adc.o
 .text.HAL_ADC_LevelOutOfWindowCallback
                0x00000000       0x14 objs/stm32l4xx_hal_adc.o
 .text.HAL_ADC_ErrorCallback
                0x00000000       0x14 objs/stm32l4xx_hal_adc.o
 .text.HAL_ADC_ConfigChannel
                0x00000000      0x7c0 objs/stm32l4xx_hal_adc.o
 .text.HAL_ADC_AnalogWDGConfig
                0x00000000      0x418 objs/stm32l4xx_hal_adc.o
 .text.HAL_ADC_GetState
                0x00000000       0x18 objs/stm32l4xx_hal_adc.o
 .text.HAL_ADC_GetError
                0x00000000       0x18 objs/stm32l4xx_hal_adc.o
 .text.ADC_ConversionStop
                0x00000000      0x178 objs/stm32l4xx_hal_adc.o
 .text.ADC_Enable
                0x00000000       0xc4 objs/stm32l4xx_hal_adc.o
 .text.ADC_Disable
                0x00000000       0xbe objs/stm32l4xx_hal_adc.o
 .text.ADC_DMAConvCplt
                0x00000000       0xd8 objs/stm32l4xx_hal_adc.o
 .text.ADC_DMAHalfConvCplt
                0x00000000       0x1c objs/stm32l4xx_hal_adc.o
 .text.ADC_DMAError
                0x00000000       0x34 objs/stm32l4xx_hal_adc.o
 .debug_info    0x00000000     0x1c56 objs/stm32l4xx_hal_adc.o
 .debug_abbrev  0x00000000      0x2cc objs/stm32l4xx_hal_adc.o
 .debug_aranges
                0x00000000      0x210 objs/stm32l4xx_hal_adc.o
 .debug_rnglists
                0x00000000      0x196 objs/stm32l4xx_hal_adc.o
 .debug_line    0x00000000     0x1635 objs/stm32l4xx_hal_adc.o
 .debug_str     0x00000000     0x10b5 objs/stm32l4xx_hal_adc.o
 .comment       0x00000000       0x27 objs/stm32l4xx_hal_adc.o
 .debug_frame   0x00000000      0x9b0 objs/stm32l4xx_hal_adc.o
 .ARM.attributes
                0x00000000       0x34 objs/stm32l4xx_hal_adc.o
 .text          0x00000000        0x0 objs/stm32l4xx_hal_adc_ex.o
 .data          0x00000000        0x0 objs/stm32l4xx_hal_adc_ex.o
 .bss           0x00000000        0x0 objs/stm32l4xx_hal_adc_ex.o
 .text.LL_ADC_SetCommonPathInternalCh
                0x00000000       0x26 objs/stm32l4xx_hal_adc_ex.o
 .text.LL_ADC_GetCommonPathInternalCh
                0x00000000       0x1c objs/stm32l4xx_hal_adc_ex.o
 .text.LL_ADC_SetCalibrationFactor
                0x00000000       0x48 objs/stm32l4xx_hal_adc_ex.o
 .text.LL_ADC_GetCalibrationFactor
                0x00000000       0x30 objs/stm32l4xx_hal_adc_ex.o
 .text.LL_ADC_SetOffset
                0x00000000       0x48 objs/stm32l4xx_hal_adc_ex.o
 .text.LL_ADC_GetOffsetChannel
                0x00000000       0x2c objs/stm32l4xx_hal_adc_ex.o
 .text.LL_ADC_SetOffsetState
                0x00000000       0x36 objs/stm32l4xx_hal_adc_ex.o
 .text.LL_ADC_REG_IsTriggerSourceSWStart
                0x00000000       0x26 objs/stm32l4xx_hal_adc_ex.o
 .text.LL_ADC_INJ_IsTriggerSourceSWStart
                0x00000000       0x26 objs/stm32l4xx_hal_adc_ex.o
 .text.LL_ADC_INJ_GetTrigAuto
                0x00000000       0x1c objs/stm32l4xx_hal_adc_ex.o
 .text.LL_ADC_INJ_SetQueueMode
                0x00000000       0x2a objs/stm32l4xx_hal_adc_ex.o
 .text.LL_ADC_SetChannelSamplingTime
                0x00000000       0x56 objs/stm32l4xx_hal_adc_ex.o
 .text.LL_ADC_SetChannelSingleDiff
                0x00000000       0x48 objs/stm32l4xx_hal_adc_ex.o
 .text.LL_ADC_EnableDeepPowerDown
                0x00000000       0x28 objs/stm32l4xx_hal_adc_ex.o
 .text.LL_ADC_DisableInternalRegulator
                0x00000000       0x24 objs/stm32l4xx_hal_adc_ex.o
 .text.LL_ADC_IsEnabled
                0x00000000       0x26 objs/stm32l4xx_hal_adc_ex.o
 .text.LL_ADC_StartCalibration
                0x00000000       0x32 objs/stm32l4xx_hal_adc_ex.o
 .text.LL_ADC_IsCalibrationOnGoing
                0x00000000       0x28 objs/stm32l4xx_hal_adc_ex.o
 .text.LL_ADC_REG_IsConversionOngoing
                0x00000000       0x26 objs/stm32l4xx_hal_adc_ex.o
 .text.LL_ADC_INJ_StartConversion
                0x00000000       0x28 objs/stm32l4xx_hal_adc_ex.o
 .text.LL_ADC_INJ_IsConversionOngoing
                0x00000000       0x26 objs/stm32l4xx_hal_adc_ex.o
 .text.HAL_ADCEx_Calibration_Start
                0x00000000       0xc0 objs/stm32l4xx_hal_adc_ex.o
 .text.HAL_ADCEx_Calibration_GetValue
                0x00000000       0x20 objs/stm32l4xx_hal_adc_ex.o
 .text.HAL_ADCEx_Calibration_SetValue
                0x00000000       0x98 objs/stm32l4xx_hal_adc_ex.o
 .text.HAL_ADCEx_InjectedStart
                0x00000000       0xe4 objs/stm32l4xx_hal_adc_ex.o
 .text.HAL_ADCEx_InjectedStop
                0x00000000       0x84 objs/stm32l4xx_hal_adc_ex.o
 .text.HAL_ADCEx_InjectedPollForConversion
                0x00000000      0x12a objs/stm32l4xx_hal_adc_ex.o
 .text.HAL_ADCEx_InjectedStart_IT
                0x00000000      0x14e objs/stm32l4xx_hal_adc_ex.o
 .text.HAL_ADCEx_InjectedStop_IT
                0x00000000       0x94 objs/stm32l4xx_hal_adc_ex.o
 .text.HAL_ADCEx_InjectedGetValue
                0x00000000       0x6e objs/stm32l4xx_hal_adc_ex.o
 .text.HAL_ADCEx_InjectedConvCpltCallback
                0x00000000       0x14 objs/stm32l4xx_hal_adc_ex.o
 .text.HAL_ADCEx_InjectedQueueOverflowCallback
                0x00000000       0x14 objs/stm32l4xx_hal_adc_ex.o
 .text.HAL_ADCEx_LevelOutOfWindow2Callback
                0x00000000       0x14 objs/stm32l4xx_hal_adc_ex.o
 .text.HAL_ADCEx_LevelOutOfWindow3Callback
                0x00000000       0x14 objs/stm32l4xx_hal_adc_ex.o
 .text.HAL_ADCEx_EndOfSamplingCallback
                0x00000000       0x14 objs/stm32l4xx_hal_adc_ex.o
 .text.HAL_ADCEx_RegularStop
                0x00000000       0x90 objs/stm32l4xx_hal_adc_ex.o
 .text.HAL_ADCEx_RegularStop_IT
                0x00000000       0xa0 objs/stm32l4xx_hal_adc_ex.o
 .text.HAL_ADCEx_RegularStop_DMA
                0x00000000       0xde objs/stm32l4xx_hal_adc_ex.o
 .text.HAL_ADCEx_InjectedConfigChannel
                0x00000000      0x940 objs/stm32l4xx_hal_adc_ex.o
 .text.HAL_ADCEx_EnableInjectedQueue
                0x00000000       0x5c objs/stm32l4xx_hal_adc_ex.o
 .text.HAL_ADCEx_DisableInjectedQueue
                0x00000000       0x4e objs/stm32l4xx_hal_adc_ex.o
 .text.HAL_ADCEx_DisableVoltageRegulator
                0x00000000       0x36 objs/stm32l4xx_hal_adc_ex.o
 .text.HAL_ADCEx_EnterADCDeepPowerDownMode
                0x00000000       0x36 objs/stm32l4xx_hal_adc_ex.o
 .debug_info    0x00000000     0x1794 objs/stm32l4xx_hal_adc_ex.o
 .debug_abbrev  0x00000000      0x2d6 objs/stm32l4xx_hal_adc_ex.o
 .debug_aranges
                0x00000000      0x170 objs/stm32l4xx_hal_adc_ex.o
 .debug_rnglists
                0x00000000      0x11a objs/stm32l4xx_hal_adc_ex.o
 .debug_line    0x00000000      0xea3 objs/stm32l4xx_hal_adc_ex.o
 .debug_str     0x00000000      0xf26 objs/stm32l4xx_hal_adc_ex.o
 .comment       0x00000000       0x27 objs/stm32l4xx_hal_adc_ex.o
 .debug_frame   0x00000000      0x68c objs/stm32l4xx_hal_adc_ex.o
 .ARM.attributes
                0x00000000       0x34 objs/stm32l4xx_hal_adc_ex.o
 .text          0x00000000        0x0 objs/stm32l4xx_hal_tim.o
 .data          0x00000000        0x0 objs/stm32l4xx_hal_tim.o
 .bss           0x00000000        0x0 objs/stm32l4xx_hal_tim.o
 .text.HAL_TIM_Base_Init
                0x00000000       0xae objs/stm32l4xx_hal_tim.o
 .text.HAL_TIM_Base_DeInit
                0x00000000       0xb8 objs/stm32l4xx_hal_tim.o
 .text.HAL_TIM_Base_MspInit
                0x00000000       0x14 objs/stm32l4xx_hal_tim.o
 .text.HAL_TIM_Base_MspDeInit
                0x00000000       0x14 objs/stm32l4xx_hal_tim.o
 .text.HAL_TIM_Base_Start
                0x00000000       0x98 objs/stm32l4xx_hal_tim.o
 .text.HAL_TIM_Base_Stop
                0x00000000       0x4e objs/stm32l4xx_hal_tim.o
 .text.HAL_TIM_Base_Start_IT
                0x00000000       0xa8 objs/stm32l4xx_hal_tim.o
 .text.HAL_TIM_Base_Stop_IT
                0x00000000       0x5e objs/stm32l4xx_hal_tim.o
 .text.HAL_TIM_Base_Start_DMA
                0x00000000      0x110 objs/stm32l4xx_hal_tim.o
 .text.HAL_TIM_Base_Stop_DMA
                0x00000000       0x64 objs/stm32l4xx_hal_tim.o
 .text.HAL_TIM_OC_Init
                0x00000000       0xae objs/stm32l4xx_hal_tim.o
 .text.HAL_TIM_OC_DeInit
                0x00000000       0xb8 objs/stm32l4xx_hal_tim.o
 .text.HAL_TIM_OC_MspInit
                0x00000000       0x14 objs/stm32l4xx_hal_tim.o
 .text.HAL_TIM_OC_MspDeInit
                0x00000000       0x14 objs/stm32l4xx_hal_tim.o
 .text.HAL_TIM_OC_Start
                0x00000000      0x1bc objs/stm32l4xx_hal_tim.o
 .text.HAL_TIM_OC_Stop
                0x00000000      0x110 objs/stm32l4xx_hal_tim.o
 .text.HAL_TIM_OC_Start_IT
                0x00000000      0x248 objs/stm32l4xx_hal_tim.o
 .text.HAL_TIM_OC_Stop_IT
                0x00000000      0x19c objs/stm32l4xx_hal_tim.o
 .text.HAL_TIM_OC_Start_DMA
                0x00000000      0x3e0 objs/stm32l4xx_hal_tim.o
 .text.HAL_TIM_OC_Stop_DMA
                0x00000000      0x1c4 objs/stm32l4xx_hal_tim.o
 .text.HAL_TIM_PWM_Init
                0x00000000       0xae objs/stm32l4xx_hal_tim.o
 .text.HAL_TIM_PWM_DeInit
                0x00000000       0xb8 objs/stm32l4xx_hal_tim.o
 .text.HAL_TIM_PWM_MspInit
                0x00000000       0x14 objs/stm32l4xx_hal_tim.o
 .text.HAL_TIM_PWM_MspDeInit
                0x00000000       0x14 objs/stm32l4xx_hal_tim.o
 .text.HAL_TIM_PWM_Start
                0x00000000      0x1bc objs/stm32l4xx_hal_tim.o
 .text.HAL_TIM_PWM_Stop
                0x00000000      0x110 objs/stm32l4xx_hal_tim.o
 .text.HAL_TIM_PWM_Start_IT
                0x00000000      0x248 objs/stm32l4xx_hal_tim.o
 .text.HAL_TIM_PWM_Stop_IT
                0x00000000      0x19c objs/stm32l4xx_hal_tim.o
 .text.HAL_TIM_PWM_Start_DMA
                0x00000000      0x3e0 objs/stm32l4xx_hal_tim.o
 .text.HAL_TIM_PWM_Stop_DMA
                0x00000000      0x1c4 objs/stm32l4xx_hal_tim.o
 .text.HAL_TIM_IC_Init
                0x00000000       0xae objs/stm32l4xx_hal_tim.o
 .text.HAL_TIM_IC_DeInit
                0x00000000       0xb8 objs/stm32l4xx_hal_tim.o
 .text.HAL_TIM_IC_MspInit
                0x00000000       0x14 objs/stm32l4xx_hal_tim.o
 .text.HAL_TIM_IC_MspDeInit
                0x00000000       0x14 objs/stm32l4xx_hal_tim.o
 .text.HAL_TIM_IC_Start
                0x00000000      0x1c0 objs/stm32l4xx_hal_tim.o
 .text.HAL_TIM_IC_Stop
                0x00000000       0xe2 objs/stm32l4xx_hal_tim.o
 .text.HAL_TIM_IC_Start_IT
                0x00000000      0x24c objs/stm32l4xx_hal_tim.o
 .text.HAL_TIM_IC_Stop_IT
                0x00000000      0x170 objs/stm32l4xx_hal_tim.o
 .text.HAL_TIM_IC_Start_DMA
                0x00000000      0x358 objs/stm32l4xx_hal_tim.o
 .text.HAL_TIM_IC_Stop_DMA
                0x00000000      0x194 objs/stm32l4xx_hal_tim.o
 .text.HAL_TIM_OnePulse_Init
                0x00000000       0xa0 objs/stm32l4xx_hal_tim.o
 .text.HAL_TIM_OnePulse_DeInit
                0x00000000       0x88 objs/stm32l4xx_hal_tim.o
 .text.HAL_TIM_OnePulse_MspInit
                0x00000000       0x14 objs/stm32l4xx_hal_tim.o
 .text.HAL_TIM_OnePulse_MspDeInit
                0x00000000       0x14 objs/stm32l4xx_hal_tim.o
 .text.HAL_TIM_OnePulse_Start
                0x00000000       0xd0 objs/stm32l4xx_hal_tim.o
 .text.HAL_TIM_OnePulse_Stop
                0x00000000       0xe4 objs/stm32l4xx_hal_tim.o
 .text.HAL_TIM_OnePulse_Start_IT
                0x00000000       0xf0 objs/stm32l4xx_hal_tim.o
 .text.HAL_TIM_OnePulse_Stop_IT
                0x00000000      0x104 objs/stm32l4xx_hal_tim.o
 .text.HAL_TIM_Encoder_Init
                0x00000000      0x14c objs/stm32l4xx_hal_tim.o
 .text.HAL_TIM_Encoder_DeInit
                0x00000000       0x88 objs/stm32l4xx_hal_tim.o
 .text.HAL_TIM_Encoder_MspInit
                0x00000000       0x14 objs/stm32l4xx_hal_tim.o
 .text.HAL_TIM_Encoder_MspDeInit
                0x00000000       0x14 objs/stm32l4xx_hal_tim.o
 .text.HAL_TIM_Encoder_Start
                0x00000000      0x11c objs/stm32l4xx_hal_tim.o
 .text.HAL_TIM_Encoder_Stop
                0x00000000      0x14e objs/stm32l4xx_hal_tim.o
 .text.HAL_TIM_Encoder_Start_IT
                0x00000000      0x15c objs/stm32l4xx_hal_tim.o
 .text.HAL_TIM_Encoder_Stop_IT
                0x00000000      0x18a objs/stm32l4xx_hal_tim.o
 .text.HAL_TIM_Encoder_Start_DMA
                0x00000000      0x2e8 objs/stm32l4xx_hal_tim.o
 .text.HAL_TIM_Encoder_Stop_DMA
                0x00000000      0x1b2 objs/stm32l4xx_hal_tim.o
 .text.HAL_TIM_IRQHandler
                0x00000000      0x23e objs/stm32l4xx_hal_tim.o
 .text.HAL_TIM_OC_ConfigChannel
                0x00000000       0xec objs/stm32l4xx_hal_tim.o
 .text.HAL_TIM_IC_ConfigChannel
                0x00000000      0x128 objs/stm32l4xx_hal_tim.o
 .text.HAL_TIM_PWM_ConfigChannel
                0x00000000      0x220 objs/stm32l4xx_hal_tim.o
 .text.HAL_TIM_OnePulse_ConfigChannel
                0x00000000      0x18c objs/stm32l4xx_hal_tim.o
 .text.HAL_TIM_DMABurst_WriteStart
                0x00000000       0x30 objs/stm32l4xx_hal_tim.o
 .text.HAL_TIM_DMABurst_MultiWriteStart
                0x00000000      0x29c objs/stm32l4xx_hal_tim.o
 .text.HAL_TIM_DMABurst_WriteStop
                0x00000000       0xe6 objs/stm32l4xx_hal_tim.o
 .text.HAL_TIM_DMABurst_ReadStart
                0x00000000       0x30 objs/stm32l4xx_hal_tim.o
 .text.HAL_TIM_DMABurst_MultiReadStart
                0x00000000      0x29c objs/stm32l4xx_hal_tim.o
 .text.HAL_TIM_DMABurst_ReadStop
                0x00000000       0xe6 objs/stm32l4xx_hal_tim.o
 .text.HAL_TIM_GenerateEvent
                0x00000000       0x4e objs/stm32l4xx_hal_tim.o
 .text.HAL_TIM_ConfigOCrefClear
                0x00000000      0x234 objs/stm32l4xx_hal_tim.o
 .text.HAL_TIM_ConfigClockSource
                0x00000000      0x18c objs/stm32l4xx_hal_tim.o
 .text.HAL_TIM_ConfigTI1Input
                0x00000000       0x38 objs/stm32l4xx_hal_tim.o
 .text.HAL_TIM_SlaveConfigSynchro
                0x00000000       0x84 objs/stm32l4xx_hal_tim.o
 .text.HAL_TIM_SlaveConfigSynchro_IT
                0x00000000       0x84 objs/stm32l4xx_hal_tim.o
 .text.HAL_TIM_ReadCapturedValue
                0x00000000       0x88 objs/stm32l4xx_hal_tim.o
 .text.HAL_TIM_PeriodElapsedCallback
                0x00000000       0x14 objs/stm32l4xx_hal_tim.o
 .text.HAL_TIM_PeriodElapsedHalfCpltCallback
                0x00000000       0x14 objs/stm32l4xx_hal_tim.o
 .text.HAL_TIM_OC_DelayElapsedCallback
                0x00000000       0x14 objs/stm32l4xx_hal_tim.o
 .text.HAL_TIM_IC_CaptureCallback
                0x00000000       0x14 objs/stm32l4xx_hal_tim.o
 .text.HAL_TIM_IC_CaptureHalfCpltCallback
                0x00000000       0x14 objs/stm32l4xx_hal_tim.o
 .text.HAL_TIM_PWM_PulseFinishedCallback
                0x00000000       0x14 objs/stm32l4xx_hal_tim.o
 .text.HAL_TIM_PWM_PulseFinishedHalfCpltCallback
                0x00000000       0x14 objs/stm32l4xx_hal_tim.o
 .text.HAL_TIM_TriggerCallback
                0x00000000       0x14 objs/stm32l4xx_hal_tim.o
 .text.HAL_TIM_TriggerHalfCpltCallback
                0x00000000       0x14 objs/stm32l4xx_hal_tim.o
 .text.HAL_TIM_ErrorCallback
                0x00000000       0x14 objs/stm32l4xx_hal_tim.o
 .text.HAL_TIM_Base_GetState
                0x00000000       0x1c objs/stm32l4xx_hal_tim.o
 .text.HAL_TIM_OC_GetState
                0x00000000       0x1c objs/stm32l4xx_hal_tim.o
 .text.HAL_TIM_PWM_GetState
                0x00000000       0x1c objs/stm32l4xx_hal_tim.o
 .text.HAL_TIM_IC_GetState
                0x00000000       0x1c objs/stm32l4xx_hal_tim.o
 .text.HAL_TIM_OnePulse_GetState
                0x00000000       0x1c objs/stm32l4xx_hal_tim.o
 .text.HAL_TIM_Encoder_GetState
                0x00000000       0x1c objs/stm32l4xx_hal_tim.o
 .text.HAL_TIM_GetActiveChannel
                0x00000000       0x18 objs/stm32l4xx_hal_tim.o
 .text.HAL_TIM_GetChannelState
                0x00000000       0x72 objs/stm32l4xx_hal_tim.o
 .text.HAL_TIM_DMABurstState
                0x00000000       0x1c objs/stm32l4xx_hal_tim.o
 .text.TIM_DMAError
                0x00000000       0x92 objs/stm32l4xx_hal_tim.o
 .text.TIM_DMADelayPulseCplt
                0x00000000       0xa8 objs/stm32l4xx_hal_tim.o
 .text.TIM_DMADelayPulseHalfCplt
                0x00000000       0x68 objs/stm32l4xx_hal_tim.o
 .text.TIM_DMACaptureCplt
                0x00000000       0xc8 objs/stm32l4xx_hal_tim.o
 .text.TIM_DMACaptureHalfCplt
                0x00000000       0x68 objs/stm32l4xx_hal_tim.o
 .text.TIM_DMAPeriodElapsedCplt
                0x00000000       0x2e objs/stm32l4xx_hal_tim.o
 .text.TIM_DMAPeriodElapsedHalfCplt
                0x00000000       0x1c objs/stm32l4xx_hal_tim.o
 .text.TIM_DMATriggerCplt
                0x00000000       0x2e objs/stm32l4xx_hal_tim.o
 .text.TIM_DMATriggerHalfCplt
                0x00000000       0x1c objs/stm32l4xx_hal_tim.o
 .text.TIM_Base_SetConfig
                0x00000000       0xc8 objs/stm32l4xx_hal_tim.o
 .text.TIM_OC1_SetConfig
                0x00000000       0xf8 objs/stm32l4xx_hal_tim.o
 .text.TIM_OC2_SetConfig
                0x00000000       0xf4 objs/stm32l4xx_hal_tim.o
 .text.TIM_OC3_SetConfig
                0x00000000       0xf0 objs/stm32l4xx_hal_tim.o
 .text.TIM_OC4_SetConfig
                0x00000000       0xbc objs/stm32l4xx_hal_tim.o
 .text.TIM_OC5_SetConfig
                0x00000000       0xb0 objs/stm32l4xx_hal_tim.o
 .text.TIM_OC6_SetConfig
                0x00000000       0xb4 objs/stm32l4xx_hal_tim.o
 .text.TIM_SlaveTimer_SetConfig
                0x00000000      0x120 objs/stm32l4xx_hal_tim.o
 .text.TIM_TI1_SetConfig
                0x00000000       0xac objs/stm32l4xx_hal_tim.o
 .text.TIM_TI1_ConfigInputStage
                0x00000000       0x5e objs/stm32l4xx_hal_tim.o
 .text.TIM_TI2_SetConfig
                0x00000000       0x7a objs/stm32l4xx_hal_tim.o
 .text.TIM_TI2_ConfigInputStage
                0x00000000       0x60 objs/stm32l4xx_hal_tim.o
 .text.TIM_TI3_SetConfig
                0x00000000       0x78 objs/stm32l4xx_hal_tim.o
 .text.TIM_TI4_SetConfig
                0x00000000       0x7a objs/stm32l4xx_hal_tim.o
 .text.TIM_ITRx_SetConfig
                0x00000000       0x36 objs/stm32l4xx_hal_tim.o
 .text.TIM_ETR_SetConfig
                0x00000000       0x40 objs/stm32l4xx_hal_tim.o
 .text.TIM_CCxChannelCmd
                0x00000000       0x4a objs/stm32l4xx_hal_tim.o
 .debug_info    0x00000000     0x28aa objs/stm32l4xx_hal_tim.o
 .debug_abbrev  0x00000000      0x26f objs/stm32l4xx_hal_tim.o
 .debug_aranges
                0x00000000      0x3e0 objs/stm32l4xx_hal_tim.o
 .debug_rnglists
                0x00000000      0x328 objs/stm32l4xx_hal_tim.o
 .debug_line    0x00000000     0x3497 objs/stm32l4xx_hal_tim.o
 .debug_str     0x00000000     0x16ec objs/stm32l4xx_hal_tim.o
 .comment       0x00000000       0x27 objs/stm32l4xx_hal_tim.o
 .debug_frame   0x00000000     0x121c objs/stm32l4xx_hal_tim.o
 .ARM.attributes
                0x00000000       0x34 objs/stm32l4xx_hal_tim.o
 .text          0x00000000        0x0 objs/gps_quectel.o
 .data          0x00000000        0x0 objs/gps_quectel.o
 .bss           0x00000000        0x0 objs/gps_quectel.o
 .rodata.OFFSET_TAB_CCMRx
                0x00000000        0x9 objs/gps_quectel.o
 .rodata.SHIFT_TAB_OCxx
                0x00000000        0x9 objs/gps_quectel.o
 .rodata.SHIFT_TAB_ICxx
                0x00000000        0x9 objs/gps_quectel.o
 .rodata.SHIFT_TAB_CCxP
                0x00000000        0x9 objs/gps_quectel.o
 .rodata.SHIFT_TAB_OISx
                0x00000000        0x9 objs/gps_quectel.o
 .bss.gps_status_pin
                0x00000000       0x1c objs/gps_quectel.o
 .bss.gps_power_switch
                0x00000000       0x1c objs/gps_quectel.o
 .text.gps_power_off
                0x00000000       0x20 objs/gps_quectel.o
 .text.gps_factory_provision
                0x00000000        0xc objs/gps_quectel.o
 .text.gps_low_res_mode
                0x00000000       0x54 objs/gps_quectel.o
 .text.gps_periodic_mode
                0x00000000       0x78 objs/gps_quectel.o
 .text.gps_standby_mode
                0x00000000        0xe objs/gps_quectel.o
 .text          0x00000000        0x0 objs/nmea.o
 .data          0x00000000        0x0 objs/nmea.o
 .bss           0x00000000        0x0 objs/nmea.o
 .rodata.OFFSET_TAB_CCMRx
                0x00000000        0x9 objs/nmea.o
 .rodata.SHIFT_TAB_OCxx
                0x00000000        0x9 objs/nmea.o
 .rodata.SHIFT_TAB_ICxx
                0x00000000        0x9 objs/nmea.o
 .rodata.SHIFT_TAB_CCxP
                0x00000000        0x9 objs/nmea.o
 .rodata.SHIFT_TAB_OISx
                0x00000000        0x9 objs/nmea.o
 .text          0x00000000        0x0 objs/settings.o
 .data          0x00000000        0x0 objs/settings.o
 .bss           0x00000000        0x0 objs/settings.o
 .rodata.OFFSET_TAB_CCMRx
                0x00000000        0x9 objs/settings.o
 .rodata.SHIFT_TAB_OCxx
                0x00000000        0x9 objs/settings.o
 .rodata.SHIFT_TAB_ICxx
                0x00000000        0x9 objs/settings.o
 .rodata.SHIFT_TAB_CCxP
                0x00000000        0x9 objs/settings.o
 .rodata.SHIFT_TAB_OISx
                0x00000000        0x9 objs/settings.o
 .bss.set_parse_ptr
                0x00000000        0x4 objs/settings.o
 .bss.set_parse_size
                0x00000000        0x4 objs/settings.o
 .bss.set_parse_line
                0x00000000       0x80 objs/settings.o
 .rodata        0x00000000       0xbd objs/settings.o
 .rodata.setting_commands
                0x00000000       0x40 objs/settings.o
 .bss.setting_buffer
                0x00000000      0x400 objs/settings.o
 .text.settings_store
                0x00000000       0x48 objs/settings.o
 .text.settings_init_all
                0x00000000       0x24 objs/settings.o
 .text.settings_init
                0x00000000       0x50 objs/settings.o
 .text.settings_dump
                0x00000000       0xc4 objs/settings.o
 .text.setting_get_char
                0x00000000       0x3c objs/settings.o
 .text.setting_parse_line
                0x00000000       0xa6 objs/settings.o
 .text.settings_execute
                0x00000000       0xb8 objs/settings.o
 .text.settings_parse
                0x00000000       0xfc objs/settings.o
 .text.settings_find_key
                0x00000000       0x50 objs/settings.o
 .text.reset_setting
                0x00000000       0x18 objs/settings.o
 .text          0x00000000        0x0 objs/gps_serial_test.o
 .data          0x00000000        0x0 objs/gps_serial_test.o
 .bss           0x00000000        0x0 objs/gps_serial_test.o
 .rodata.OFFSET_TAB_CCMRx
                0x00000000        0x9 objs/gps_serial_test.o
 .rodata.SHIFT_TAB_OCxx
                0x00000000        0x9 objs/gps_serial_test.o
 .rodata.SHIFT_TAB_ICxx
                0x00000000        0x9 objs/gps_serial_test.o
 .rodata.SHIFT_TAB_CCxP
                0x00000000        0x9 objs/gps_serial_test.o
 .rodata.SHIFT_TAB_OISx
                0x00000000        0x9 objs/gps_serial_test.o
 .bss.gps_op    0x00000000      0x100 objs/gps_serial_test.o
 .text          0x00000000      0x254 /usr/lib/gcc/arm-none-eabi/13.2.1/thumb/v7e-m+fp/hard/libgcc.a(_arm_muldf3.o)
 .data          0x00000000        0x0 /usr/lib/gcc/arm-none-eabi/13.2.1/thumb/v7e-m+fp/hard/libgcc.a(_arm_muldf3.o)
 .bss           0x00000000        0x0 /usr/lib/gcc/arm-none-eabi/13.2.1/thumb/v7e-m+fp/hard/libgcc.a(_arm_muldf3.o)
 .debug_line    0x00000000       0xf5 /usr/lib/gcc/arm-none-eabi/13.2.1/thumb/v7e-m+fp/hard/libgcc.a(_arm_muldf3.o)
 .debug_line_str
                0x00000000       0x9c /usr/lib/gcc/arm-none-eabi/13.2.1/thumb/v7e-m+fp/hard/libgcc.a(_arm_muldf3.o)
 .debug_info    0x00000000       0x3f /usr/lib/gcc/arm-none-eabi/13.2.1/thumb/v7e-m+fp/hard/libgcc.a(_arm_muldf3.o)
 .debug_abbrev  0x00000000       0x26 /usr/lib/gcc/arm-none-eabi/13.2.1/thumb/v7e-m+fp/hard/libgcc.a(_arm_muldf3.o)
 .debug_aranges
                0x00000000       0x20 /usr/lib/gcc/arm-none-eabi/13.2.1/thumb/v7e-m+fp/hard/libgcc.a(_arm_muldf3.o)
 .debug_str     0x00000000       0xbe /usr/lib/gcc/arm-none-eabi/13.2.1/thumb/v7e-m+fp/hard/libgcc.a(_arm_muldf3.o)
 .debug_frame   0x00000000       0x30 /usr/lib/gcc/arm-none-eabi/13.2.1/thumb/v7e-m+fp/hard/libgcc.a(_arm_muldf3.o)
 .ARM.attributes
                0x00000000       0x22 /usr/lib/gcc/arm-none-eabi/13.2.1/thumb/v7e-m+fp/hard/libgcc.a(_arm_muldf3.o)
 .data          0x00000000        0x0 /usr/lib/gcc/arm-none-eabi/13.2.1/thumb/v7e-m+fp/hard/libgcc.a(_arm_addsubdf3.o)
 .bss           0x00000000        0x0 /usr/lib/gcc/arm-none-eabi/13.2.1/thumb/v7e-m+fp/hard/libgcc.a(_arm_addsubdf3.o)
 .data          0x00000000        0x0 /usr/lib/gcc/arm-none-eabi/13.2.1/thumb/v7e-m+fp/hard/libgcc.a(_arm_muldivdf3.o)
 .bss           0x00000000        0x0 /usr/lib/gcc/arm-none-eabi/13.2.1/thumb/v7e-m+fp/hard/libgcc.a(_arm_muldivdf3.o)
 .text          0x00000000       0x50 /usr/lib/gcc/arm-none-eabi/13.2.1/thumb/v7e-m+fp/hard/libgcc.a(_arm_fixdfsi.o)
 .data          0x00000000        0x0 /usr/lib/gcc/arm-none-eabi/13.2.1/thumb/v7e-m+fp/hard/libgcc.a(_arm_fixdfsi.o)
 .bss           0x00000000        0x0 /usr/lib/gcc/arm-none-eabi/13.2.1/thumb/v7e-m+fp/hard/libgcc.a(_arm_fixdfsi.o)
 .debug_line    0x00000000       0x62 /usr/lib/gcc/arm-none-eabi/13.2.1/thumb/v7e-m+fp/hard/libgcc.a(_arm_fixdfsi.o)
 .debug_line_str
                0x00000000       0x9c /usr/lib/gcc/arm-none-eabi/13.2.1/thumb/v7e-m+fp/hard/libgcc.a(_arm_fixdfsi.o)
 .debug_info    0x00000000       0x3c /usr/lib/gcc/arm-none-eabi/13.2.1/thumb/v7e-m+fp/hard/libgcc.a(_arm_fixdfsi.o)
 .debug_abbrev  0x00000000       0x28 /usr/lib/gcc/arm-none-eabi/13.2.1/thumb/v7e-m+fp/hard/libgcc.a(_arm_fixdfsi.o)
 .debug_aranges
                0x00000000       0x20 /usr/lib/gcc/arm-none-eabi/13.2.1/thumb/v7e-m+fp/hard/libgcc.a(_arm_fixdfsi.o)
 .debug_str     0x00000000       0xbf /usr/lib/gcc/arm-none-eabi/13.2.1/thumb/v7e-m+fp/hard/libgcc.a(_arm_fixdfsi.o)
 .debug_frame   0x00000000       0x24 /usr/lib/gcc/arm-none-eabi/13.2.1/thumb/v7e-m+fp/hard/libgcc.a(_arm_fixdfsi.o)
 .ARM.attributes
                0x00000000       0x22 /usr/lib/gcc/arm-none-eabi/13.2.1/thumb/v7e-m+fp/hard/libgcc.a(_arm_fixdfsi.o)
 .data          0x00000000        0x0 /usr/lib/gcc/arm-none-eabi/13.2.1/thumb/v7e-m+fp/hard/libgcc.a(_arm_truncdfsf2.o)
 .bss           0x00000000        0x0 /usr/lib/gcc/arm-none-eabi/13.2.1/thumb/v7e-m+fp/hard/libgcc.a(_arm_truncdfsf2.o)
 .data          0x00000000        0x0 /usr/lib/gcc/arm-none-eabi/13.2.1/thumb/v7e-m+fp/hard/libgcc.a(_aeabi_uldivmod.o)
 .bss           0x00000000        0x0 /usr/lib/gcc/arm-none-eabi/13.2.1/thumb/v7e-m+fp/hard/libgcc.a(_aeabi_uldivmod.o)
 .data          0x00000000        0x0 /usr/lib/gcc/arm-none-eabi/13.2.1/thumb/v7e-m+fp/hard/libgcc.a(_udivmoddi4.o)
 .bss           0x00000000        0x0 /usr/lib/gcc/arm-none-eabi/13.2.1/thumb/v7e-m+fp/hard/libgcc.a(_udivmoddi4.o)
 .ARM.extab     0x00000000        0x0 /usr/lib/gcc/arm-none-eabi/13.2.1/thumb/v7e-m+fp/hard/libgcc.a(_udivmoddi4.o)
 .data          0x00000000        0x0 /usr/lib/gcc/arm-none-eabi/13.2.1/thumb/v7e-m+fp/hard/libgcc.a(_dvmd_tls.o)
 .bss           0x00000000        0x0 /usr/lib/gcc/arm-none-eabi/13.2.1/thumb/v7e-m+fp/hard/libgcc.a(_dvmd_tls.o)
 .data          0x00000000        0x0 /usr/lib/gcc/arm-none-eabi/13.2.1/../../../arm-none-eabi/lib/thumb/v7e-m+fp/hard/libm.a(libm_a-sf_trunc.o)
 .bss           0x00000000        0x0 /usr/lib/gcc/arm-none-eabi/13.2.1/../../../arm-none-eabi/lib/thumb/v7e-m+fp/hard/libm.a(libm_a-sf_trunc.o)

Memory Configuration

Name             Origin             Length             Attributes
FLASH            0x08000000         0x00040000         xr
RAM              0x20000190         0x0000fe70         xrw
*default*        0x00000000         0xffffffff

Linker script and memory map

LOAD objs/startup_stm32l431xx.o
LOAD objs/SVC_Table.o
LOAD objs/HAL_CM4.o
LOAD objs/HAL_CM.o
LOAD objs/RTX_Conf_CM.o
LOAD objs/rt_CMSIS.o
LOAD objs/rt_Event.o
LOAD objs/rt_List.o
LOAD objs/rt_Mailbox.o
LOAD objs/rt_MemBox.o
LOAD objs/rt_Memory.o
LOAD objs/rt_Mutex.o
LOAD objs/rt_Robin.o
LOAD objs/rt_Semaphore.o
LOAD objs/rt_System.o
LOAD objs/rt_Task.o
LOAD objs/rt_Time.o
LOAD objs/system_stm32l4xx.o
LOAD objs/sleep.o
LOAD objs/start.o
LOAD objs/board_fault.o
LOAD objs/bits.o
LOAD objs/printf.o
LOAD objs/libc.o
LOAD objs/chip_uid.o
LOAD objs/alarm.o
LOAD objs/rtc_api.o
LOAD objs/semihost_api.o
LOAD objs/us_ticker.o
LOAD objs/wait_api.o
LOAD objs/mcu_sleep.o
LOAD objs/os_idle.o
LOAD objs/datetime.o
LOAD objs/console.o
LOAD objs/event.o
LOAD objs/system_file.o
LOAD objs/uid.o
LOAD objs/crc16.o
LOAD objs/announce.o
LOAD objs/analogin_api.o
LOAD objs/analogin_device.o
LOAD objs/dma.o
LOAD objs/gpio.o
LOAD objs/gpio_api.o
LOAD objs/gpio_irq_api.o
LOAD objs/gpio_irq_device.o
LOAD objs/PeripheralPins.o
LOAD objs/pinmap.o
LOAD objs/mbed_pinmap_common.o
LOAD objs/mbed_us_ticker_api.o
LOAD objs/serial_api.o
LOAD objs/serial_device.o
LOAD objs/i2c_api.o
LOAD objs/port_stubs.o
LOAD objs/serial_api_stubs.o
LOAD objs/serial_wire_debug.o
LOAD objs/stm32l4xx_hal.o
LOAD objs/stm32l4xx_hal_cortex.o
LOAD objs/stm32l4xx_hal_pwr.o
LOAD objs/stm32l4xx_hal_pwr_ex.o
LOAD objs/stm32l4xx_hal_rtc.o
LOAD objs/stm32l4xx_hal_rtc_ex.o
LOAD objs/stm32l4xx_hal_rcc.o
LOAD objs/stm32l4xx_hal_rcc_ex.o
LOAD objs/stm32l4xx_hal_uart.o
LOAD objs/stm32l4xx_hal_uart_ex.o
LOAD objs/stm32l4xx_hal_i2c.o
LOAD objs/stm32l4xx_hal_i2c_ex.o
LOAD objs/stm32l4xx_hal_adc.o
LOAD objs/stm32l4xx_hal_adc_ex.o
LOAD objs/stm32l4xx_hal_tim.o
LOAD objs/gps_quectel.o
LOAD objs/nmea.o
LOAD objs/settings.o
LOAD objs/gps_serial_test.o
LOAD /usr/lib/gcc/arm-none-eabi/13.2.1/thumb/v7e-m+fp/hard/libgcc.a
LOAD /usr/lib/gcc/arm-none-eabi/13.2.1/../../../arm-none-eabi/lib/thumb/v7e-m+fp/hard/libnosys.a
LOAD /usr/lib/gcc/arm-none-eabi/13.2.1/../../../arm-none-eabi/lib/thumb/v7e-m+fp/hard/libm.a

.text           0x08000000     0x9b4c
 *(.isr_vector)
 .isr_vector    0x08000000      0x18c objs/startup_stm32l431xx.o
                0x08000000                g_pfnVectors
 *(.text*)
 .text.Reset_Handler
                0x0800018c       0x4c objs/startup_stm32l431xx.o
                0x0800018c                Reset_Handler
 .text.Default_Handler
                0x080001d8        0x2 objs/startup_stm32l431xx.o
                0x080001d8                RTC_Alarm_IRQHandler
                0x080001d8                EXTI2_IRQHandler
                0x080001d8                DebugMon_Handler
                0x080001d8                TIM1_CC_IRQHandler
                0x080001d8                TSC_IRQHandler
                0x080001d8                TAMP_STAMP_IRQHandler
                0x080001d8                NMI_Handler
                0x080001d8                EXTI3_IRQHandler
                0x080001d8                LPTIM2_IRQHandler
                0x080001d8                I2C3_ER_IRQHandler
                0x080001d8                EXTI0_IRQHandler
                0x080001d8                I2C2_EV_IRQHandler
                0x080001d8                CAN1_RX0_IRQHandler
                0x080001d8                FPU_IRQHandler
                0x080001d8                TIM1_UP_TIM16_IRQHandler
                0x080001d8                UsageFault_Handler
                0x080001d8                SPI1_IRQHandler
                0x080001d8                TIM6_DAC_IRQHandler
                0x080001d8                DMA2_Channel2_IRQHandler
                0x080001d8                DMA1_Channel4_IRQHandler
                0x080001d8                ADC1_IRQHandler
                0x080001d8                USART3_IRQHandler
                0x080001d8                DMA1_Channel7_IRQHandler
                0x080001d8                CAN1_RX1_IRQHandler
                0x080001d8                DMA2_Channel1_IRQHandler
                0x080001d8                QUADSPI_IRQHandler
                0x080001d8                I2C1_EV_IRQHandler
                0x080001d8                DMA1_Channel6_IRQHandler
                0x080001d8                DMA2_Channel4_IRQHandler
                0x080001d8                RCC_IRQHandler
                0x080001d8                TIM1_TRG_COM_IRQHandler
                0x080001d8                DMA1_Channel1_IRQHandler
                0x080001d8                Default_Handler
                0x080001d8                DMA2_Channel7_IRQHandler
                0x080001d8                EXTI15_10_IRQHandler
                0x080001d8                TIM7_IRQHandler
                0x080001d8                SDMMC1_IRQHandler
                0x080001d8                I2C3_EV_IRQHandler
                0x080001d8                EXTI9_5_IRQHandler
                0x080001d8                RTC_WKUP_IRQHandler
                0x080001d8                PVD_PVM_IRQHandler
                0x080001d8                SPI2_IRQHandler
                0x080001d8                MemManage_Handler
                0x080001d8                CAN1_TX_IRQHandler
                0x080001d8                DMA2_Channel5_IRQHandler
                0x080001d8                CRS_IRQHandler
                0x080001d8                DMA1_Channel5_IRQHandler
                0x080001d8                EXTI4_IRQHandler
                0x080001d8                RNG_IRQHandler
                0x080001d8                DMA1_Channel3_IRQHandler
                0x080001d8                COMP_IRQHandler
                0x080001d8                WWDG_IRQHandler
                0x080001d8                LPUART1_IRQHandler
                0x080001d8                DMA2_Channel6_IRQHandler
                0x080001d8                TIM2_IRQHandler
                0x080001d8                EXTI1_IRQHandler
                0x080001d8                USART2_IRQHandler
                0x080001d8                I2C2_ER_IRQHandler
                0x080001d8                DMA1_Channel2_IRQHandler
                0x080001d8                CAN1_SCE_IRQHandler
                0x080001d8                FLASH_IRQHandler
                0x080001d8                BusFault_Handler
                0x080001d8                USART1_IRQHandler
                0x080001d8                SPI3_IRQHandler
                0x080001d8                I2C1_ER_IRQHandler
                0x080001d8                SWPMI1_IRQHandler
                0x080001d8                LPTIM1_IRQHandler
                0x080001d8                SAI1_IRQHandler
                0x080001d8                DMA2_Channel3_IRQHandler
                0x080001d8                TIM1_BRK_TIM15_IRQHandler
 *fill*         0x080001da        0x2 
 .text          0x080001dc      0x198 objs/HAL_CM4.o
                0x080001dc                rt_set_PSP
                0x080001e2                rt_get_PSP
                0x080001e8                os_set_env
                0x08000200                _alloc_box
                0x0800021c                _free_box
                0x08000238                SVC_Handler
                0x080002de                PendSV_Handler
                0x080002e4                Sys_Switch
                0x0800033e                SysTick_Handler
                0x08000348                OS_Tick_Handler
 .text.rt_init_stack
                0x08000374      0x104 objs/HAL_CM.o
                0x08000374                rt_init_stack
 .text.rt_ret_regs
                0x08000478       0x2c objs/HAL_CM.o
 .text.rt_ret_val
                0x080004a4       0x20 objs/HAL_CM.o
                0x080004a4                rt_ret_val
 .text.rt_ret_val2
                0x080004c4       0x2a objs/HAL_CM.o
                0x080004c4                rt_ret_val2
 *fill*         0x080004ee        0x2 
 .text.rt_ms2tick
                0x080004f0       0x68 objs/rt_CMSIS.o
 .text.rt_tid2ptcb
                0x08000558       0x3e objs/rt_CMSIS.o
 .text.rt_id2obj
                0x08000596       0x24 objs/rt_CMSIS.o
 *fill*         0x080005ba        0x2 
 .text.svcKernelInitialize
                0x080005bc       0x98 objs/rt_CMSIS.o
                0x080005bc                svcKernelInitialize
 .text.svcKernelStart
                0x08000654       0x70 objs/rt_CMSIS.o
                0x08000654                svcKernelStart
 .text.osKernelInitialize
                0x080006c4       0x44 objs/rt_CMSIS.o
                0x080006c4                osKernelInitialize
 .text.osKernelStart
                0x08000708       0xe8 objs/rt_CMSIS.o
                0x08000708                osKernelStart
 .text.sysThreadError
                0x080007f0       0x14 objs/rt_CMSIS.o
 .text.svcThreadCreate
                0x08000804       0xd0 objs/rt_CMSIS.o
                0x08000804                svcThreadCreate
 .text.svcThreadGetId
                0x080008d4       0x2c objs/rt_CMSIS.o
                0x080008d4                svcThreadGetId
 .text.svcThreadTerminate
                0x08000900       0x60 objs/rt_CMSIS.o
                0x08000900                svcThreadTerminate
 .text.osThreadCreate
                0x08000960       0x64 objs/rt_CMSIS.o
                0x08000960                osThreadCreate
 .text.osThreadGetId
                0x080009c4       0x2c objs/rt_CMSIS.o
                0x080009c4                osThreadGetId
 .text.osThreadExit
                0x080009f0       0x20 objs/rt_CMSIS.o
                0x080009f0                osThreadExit
 .text.svcDelay
                0x08000a10       0x2c objs/rt_CMSIS.o
                0x08000a10                svcDelay
 .text.osDelay  0x08000a3c       0x34 objs/rt_CMSIS.o
                0x08000a3c                osDelay
 .text.rt_timer_insert
                0x08000a70       0x84 objs/rt_CMSIS.o
 .text.svcTimerCall
                0x08000af4       0x60 objs/rt_CMSIS.o
                0x08000af4                svcTimerCall
 .text.sysTimerTick
                0x08000b54       0x88 objs/rt_CMSIS.o
                0x08000b54                sysTimerTick
 .text.osTimerCall
                0x08000bdc       0x34 objs/rt_CMSIS.o
                0x08000bdc                osTimerCall
 .text.osTimerThread
                0x08000c10       0x44 objs/rt_CMSIS.o
                0x08000c10                osTimerThread
 .text.svcSignalWait
                0x08000c54       0xcc objs/rt_CMSIS.o
                0x08000c54                svcSignalWait
 .text.osSignalWait
                0x08000d20       0x64 objs/rt_CMSIS.o
                0x08000d20                osSignalWait
 .text.svcMutexWait
                0x08000d84       0x5a objs/rt_CMSIS.o
                0x08000d84                svcMutexWait
 .text.svcMutexRelease
                0x08000dde       0x42 objs/rt_CMSIS.o
                0x08000dde                svcMutexRelease
 .text.osMutexWait
                0x08000e20       0x3c objs/rt_CMSIS.o
                0x08000e20                osMutexWait
 .text.osMutexRelease
                0x08000e5c       0x34 objs/rt_CMSIS.o
                0x08000e5c                osMutexRelease
 .text.svcMessageCreate
                0x08000e90       0x62 objs/rt_CMSIS.o
                0x08000e90                svcMessageCreate
 .text.svcMessageGet
                0x08000ef2       0xca objs/rt_CMSIS.o
                0x08000ef2                svcMessageGet
 .text.isrMessagePut
                0x08000fbc       0x4c objs/rt_CMSIS.o
                0x08000fbc                isrMessagePut
 .text.isrMessageGet
                0x08001008       0x94 objs/rt_CMSIS.o
                0x08001008                isrMessageGet
 .text.osMessageGet
                0x0800109c       0x5c objs/rt_CMSIS.o
                0x0800109c                osMessageGet
 .text.rt_evt_wait
                0x080010f8       0xb8 objs/rt_Event.o
                0x080010f8                rt_evt_wait
 .text.rt_evt_psh
                0x080011b0       0xa8 objs/rt_Event.o
                0x080011b0                rt_evt_psh
 .text.rt_inc_qi
                0x08001258       0x60 objs/rt_List.o
 .text.rt_put_prio
                0x080012b8       0x8c objs/rt_List.o
                0x080012b8                rt_put_prio
 .text.rt_get_first
                0x08001344       0x60 objs/rt_List.o
                0x08001344                rt_get_first
 .text.rt_put_rdy_first
                0x080013a4       0x2c objs/rt_List.o
                0x080013a4                rt_put_rdy_first
 .text.rt_resort_prio
                0x080013d0       0x50 objs/rt_List.o
                0x080013d0                rt_resort_prio
 .text.rt_put_dly
                0x08001420       0xcc objs/rt_List.o
                0x08001420                rt_put_dly
 .text.rt_dec_dly
                0x080014ec       0xcc objs/rt_List.o
                0x080014ec                rt_dec_dly
 .text.rt_rmv_list
                0x080015b8       0x68 objs/rt_List.o
                0x080015b8                rt_rmv_list
 .text.rt_rmv_dly
                0x08001620       0x5c objs/rt_List.o
                0x08001620                rt_rmv_dly
 .text.rt_psq_enq
                0x0800167c       0x58 objs/rt_List.o
                0x0800167c                rt_psq_enq
 .text.rt_mbx_init
                0x080016d4       0x52 objs/rt_Mailbox.o
                0x080016d4                rt_mbx_init
 *fill*         0x08001726        0x2 
 .text.rt_mbx_wait
                0x08001728      0x11c objs/rt_Mailbox.o
                0x08001728                rt_mbx_wait
 .text.rt_mbx_check
                0x08001844       0x24 objs/rt_Mailbox.o
                0x08001844                rt_mbx_check
 .text.isr_mbx_send
                0x08001868       0x24 objs/rt_Mailbox.o
                0x08001868                isr_mbx_send
 .text.isr_mbx_receive
                0x0800188c       0x80 objs/rt_Mailbox.o
                0x0800188c                isr_mbx_receive
 .text.rt_mbx_psh
                0x0800190c      0x164 objs/rt_Mailbox.o
                0x0800190c                rt_mbx_psh
 .text._init_box
                0x08001a70       0xac objs/rt_MemBox.o
                0x08001a70                _init_box
 .text.rt_alloc_box
                0x08001b1c       0x44 objs/rt_MemBox.o
                0x08001b1c                rt_alloc_box
 .text.rt_free_box
                0x08001b60       0x56 objs/rt_MemBox.o
                0x08001b60                rt_free_box
 .text.rt_init_mem
                0x08001bb6       0x48 objs/rt_Memory.o
                0x08001bb6                rt_init_mem
 .text.rt_alloc_mem
                0x08001bfe       0xa8 objs/rt_Memory.o
                0x08001bfe                rt_alloc_mem
 .text.rt_free_mem
                0x08001ca6       0x6a objs/rt_Memory.o
                0x08001ca6                rt_free_mem
 .text.rt_mut_release
                0x08001d10      0x174 objs/rt_Mutex.o
                0x08001d10                rt_mut_release
 .text.rt_mut_wait
                0x08001e84       0xc8 objs/rt_Mutex.o
                0x08001e84                rt_mut_wait
 .text.rt_init_robin
                0x08001f4c       0x28 objs/rt_Robin.o
                0x08001f4c                rt_init_robin
 .text.rt_chk_robin
                0x08001f74       0x68 objs/rt_Robin.o
                0x08001f74                rt_chk_robin
 .text.rt_sem_psh
                0x08001fdc       0x50 objs/rt_Semaphore.o
                0x08001fdc                rt_sem_psh
 .text.rt_systick_init
                0x0800202c       0x44 objs/rt_System.o
 .text.rt_psh_req
                0x08002070       0x34 objs/rt_System.o
                0x08002070                rt_psh_req
 .text.rt_pop_req
                0x080020a4       0xd0 objs/rt_System.o
                0x080020a4                rt_pop_req
 .text.os_tick_init
                0x08002174       0x10 objs/rt_System.o
                0x08002174                os_tick_init
 .text.os_tick_irqack
                0x08002184        0xe objs/rt_System.o
                0x08002184                os_tick_irqack
 *fill*         0x08002192        0x2 
 .text.rt_systick
                0x08002194       0x54 objs/rt_System.o
                0x08002194                rt_systick
 .text.rt_stk_check
                0x080021e8       0x34 objs/rt_System.o
                0x080021e8                rt_stk_check
 .text.rt_svc_init
                0x0800221c       0x80 objs/rt_Task.o
 .text.rt_get_TID
                0x0800229c       0x48 objs/rt_Task.o
 .text.rt_init_context
                0x080022e4       0x90 objs/rt_Task.o
 .text.rt_switch_req
                0x08002374       0x24 objs/rt_Task.o
                0x08002374                rt_switch_req
 .text.rt_dispatch
                0x08002398       0x64 objs/rt_Task.o
                0x08002398                rt_dispatch
 .text.rt_block
                0x080023fc       0x54 objs/rt_Task.o
                0x080023fc                rt_block
 .text.rt_tsk_self
                0x08002450       0x24 objs/rt_Task.o
                0x08002450                rt_tsk_self
 .text.rt_tsk_prio
                0x08002474       0xcc objs/rt_Task.o
                0x08002474                rt_tsk_prio
 .text.rt_tsk_create
                0x08002540       0x84 objs/rt_Task.o
                0x08002540                rt_tsk_create
 .text.rt_tsk_delete
                0x080025c4      0x1ec objs/rt_Task.o
                0x080025c4                rt_tsk_delete
 .text.rt_sys_init
                0x080027b0      0x108 objs/rt_Task.o
                0x080027b0                rt_sys_init
 .text.rt_sys_start
                0x080028b8       0x50 objs/rt_Task.o
                0x080028b8                rt_sys_start
 .text.rt_dly_wait
                0x08002908       0x1c objs/rt_Time.o
                0x08002908                rt_dly_wait
 .text.SystemInit
                0x08002924       0x6c objs/system_stm32l4xx.o
                0x08002924                SystemInit
 .text._start   0x08002990       0x2c objs/start.o
                0x08002990                _start
 .text.__NVIC_SystemReset
                0x080029bc       0x2c objs/board_fault.o
 .text.board_fault_save
                0x080029e8       0x20 objs/board_fault.o
                0x080029e8                board_fault_save
 .text.os_error
                0x08002a08       0x5c objs/board_fault.o
                0x08002a08                os_error
 .text.die      0x08002a64        0xa objs/board_fault.o
                0x08002a64                die
 *fill*         0x08002a6e        0x2 
 .text.mbed_assert_internal
                0x08002a70       0x24 objs/board_fault.o
                0x08002a70                mbed_assert_internal
 .text.pop_registers_from_fault_stack
                0x08002a94       0xcc objs/board_fault.o
                0x08002a94                pop_registers_from_fault_stack
 .text.HardFault_Handler
                0x08002b60       0x28 objs/board_fault.o
                0x08002b60                HardFault_Handler
 .text.store_uint16
                0x08002b88       0x36 objs/bits.o
                0x08002b88                store_uint16
 .text.store_uint32
                0x08002bbe       0x4e objs/bits.o
                0x08002bbe                store_uint32
 .text.printf_put
                0x08002c0c       0x54 objs/printf.o
                0x08002c0c                printf_put
 .text.ee_skip_atoi
                0x08002c60       0x4e objs/printf.o
 *fill*         0x08002cae        0x2 
 .text.ee_number
                0x08002cb0      0x224 objs/printf.o
 .text.vsprintf_common
                0x08002ed4      0x49c objs/printf.o
                0x08002ed4                vsprintf_common
 .text.vsprintf
                0x08003370       0x3a objs/printf.o
                0x08003370                vsprintf
 .text.printf_putchar
                0x080033aa       0x36 objs/printf.o
                0x080033aa                printf_putchar
 .text.printf   0x080033e0       0x54 objs/printf.o
                0x080033e0                printf
 .text.sprintf  0x08003434       0x44 objs/printf.o
                0x08003434                sprintf
 .text.uart_putchar
                0x08003478       0x54 objs/printf.o
                0x08003478                uart_putchar
 .text.uart_printf
                0x080034cc       0x3c objs/printf.o
                0x080034cc                uart_printf
 .text.error    0x08003508       0x4c objs/printf.o
                0x08003508                error
 .text.memset   0x08003554       0x38 objs/libc.o
                0x08003554                memset
 .text.strlen   0x0800358c       0x2e objs/libc.o
                0x0800358c                strlen
 .text.strnlen  0x080035ba       0x3a objs/libc.o
                0x080035ba                strnlen
 .text.strcmp   0x080035f4       0x4e objs/libc.o
                0x080035f4                strcmp
 .text.strncmp  0x08003642       0x5e objs/libc.o
                0x08003642                strncmp
 .text.atoi     0x080036a0       0xee objs/libc.o
                0x080036a0                atoi
 *fill*         0x0800378e        0x2 
 .text.atof     0x08003790      0x104 objs/libc.o
                0x08003790                atof
 .text.rtc_read
                0x08003894      0x138 objs/rtc_api.o
                0x08003894                rtc_read
 .text.os_idle_demon
                0x080039cc        0x8 objs/os_idle.o
                0x080039cc                os_idle_demon
 .text.datetime_to_seconds
                0x080039d4       0x4c objs/datetime.o
                0x080039d4                datetime_to_seconds
 .text.check_if_leap
                0x08003a20       0x6c objs/datetime.o
 .text.date_to_seconds
                0x08003a8c       0xd8 objs/datetime.o
                0x08003a8c                date_to_seconds
 .text.seconds_to_datetime
                0x08003b64      0x184 objs/datetime.o
                0x08003b64                seconds_to_datetime
 .text.datetime_display
                0x08003ce8       0x60 objs/datetime.o
                0x08003ce8                datetime_display
 .text.console_write
                0x08003d48       0x1a objs/console.o
                0x08003d48                console_write
 .text.console_flush
                0x08003d62        0xe objs/console.o
                0x08003d62                console_flush
 .text.event_display
                0x08003d70      0x1b8 objs/event.o
                0x08003d70                event_display
 .text.event_type_normalize
                0x08003f28       0x24 objs/event.o
                0x08003f28                event_type_normalize
 .text.event_store
                0x08003f4c      0x31c objs/event.o
                0x08003f4c                event_store
 .text.event_log
                0x08004268       0xd4 objs/event.o
                0x08004268                event_log
 .text.sys_file_backup_init
                0x0800433c       0x54 objs/system_file.o
                0x0800433c                sys_file_backup_init
 .text.sys_file_backup_write_reg
                0x08004390       0x40 objs/system_file.o
                0x08004390                sys_file_backup_write_reg
 .text.sys_file_backup_read_reg
                0x080043d0       0x40 objs/system_file.o
                0x080043d0                sys_file_backup_read_reg
 .text.sys_file_load_from_backup
                0x08004410       0x40 objs/system_file.o
 .text.sys_file_save_to_backup
                0x08004450       0x3c objs/system_file.o
 .text.sys_file_sync_to_backup
                0x0800448c        0xc objs/system_file.o
                0x0800448c                sys_file_sync_to_backup
 .text.sys_file_init
                0x08004498       0xd4 objs/system_file.o
                0x08004498                sys_file_init
 .text.gpio_write
                0x0800456c       0x32 objs/gpio.o
 .text._gpio_init_in
                0x0800459e       0x40 objs/gpio.o
 .text._gpio_init_out
                0x080045de       0x4a objs/gpio.o
 .text.gpio_init_in
                0x08004628       0x22 objs/gpio.o
                0x08004628                gpio_init_in
 .text.gpio_init_in_ex
                0x0800464a       0x26 objs/gpio.o
                0x0800464a                gpio_init_in_ex
 .text.gpio_init_out
                0x08004670       0x22 objs/gpio.o
                0x08004670                gpio_init_out
 .text.gpio_init_out_ex
                0x08004692       0x24 objs/gpio.o
                0x08004692                gpio_init_out_ex
 .text.LL_GPIO_SetPinMode
                0x080046b6       0x7a objs/gpio_api.o
 .text.Set_GPIO_Clock
                0x08004730      0x11c objs/gpio_api.o
                0x08004730                Set_GPIO_Clock
 .text.gpio_set
                0x0800484c       0x48 objs/gpio_api.o
                0x0800484c                gpio_set
 .text.gpio_init
                0x08004894       0x88 objs/gpio_api.o
                0x08004894                gpio_init
 .text.gpio_mode
                0x0800491c       0x24 objs/gpio_api.o
                0x0800491c                gpio_mode
 .text.gpio_dir
                0x08004940       0x3c objs/gpio_api.o
                0x08004940                gpio_dir
 .text.LL_GPIO_SetPinMode
                0x0800497c       0x7a objs/pinmap.o
 .text.LL_GPIO_GetPinMode
                0x080049f6       0x6e objs/pinmap.o
 .text.LL_GPIO_SetPinOutputType
                0x08004a64       0x30 objs/pinmap.o
 .text.LL_GPIO_SetPinSpeed
                0x08004a94       0x7a objs/pinmap.o
 .text.LL_GPIO_SetPinPull
                0x08004b0e       0x7a objs/pinmap.o
 .text.LL_GPIO_SetAFPin_0_7
                0x08004b88       0x7a objs/pinmap.o
 .text.LL_GPIO_SetAFPin_8_15
                0x08004c02       0x7e objs/pinmap.o
 .text.stm_pin_DisconnectDebug
                0x08004c80       0x16 objs/pinmap.o
 .text.stm_pin_PullConfig
                0x08004c96       0x46 objs/pinmap.o
 .text.stm_pin_SetAFPin
                0x08004cdc       0x48 objs/pinmap.o
 .text.pin_function
                0x08004d24      0x130 objs/pinmap.o
                0x08004d24                pin_function
 .text.pin_mode
                0x08004e54       0xc4 objs/pinmap.o
                0x08004e54                pin_mode
 .text.pinmap_merge
                0x08004f18       0x40 objs/mbed_pinmap_common.o
                0x08004f18                pinmap_merge
 .text.pinmap_find_peripheral
                0x08004f58       0x44 objs/mbed_pinmap_common.o
                0x08004f58                pinmap_find_peripheral
 .text.pinmap_peripheral
                0x08004f9c       0x48 objs/mbed_pinmap_common.o
                0x08004f9c                pinmap_peripheral
 .text.pinmap_find_function
                0x08004fe4       0x44 objs/mbed_pinmap_common.o
                0x08004fe4                pinmap_find_function
 .text.debug    0x08005028       0x4c objs/serial_api.o
 .text._serial_init_direct
                0x08005074      0x254 objs/serial_api.o
 .text.serial_init
                0x080052c8       0xa0 objs/serial_api.o
                0x080052c8                serial_init
 .text.serial_baud
                0x08005368       0x64 objs/serial_api.o
                0x08005368                serial_baud
 .text.serial_format
                0x080053cc       0xd4 objs/serial_api.o
                0x080053cc                serial_format
 .text.init_uart
                0x080054a0       0xf0 objs/serial_api.o
                0x080054a0                init_uart
 .text.get_uart_index
                0x08005590       0x7c objs/serial_api.o
                0x08005590                get_uart_index
 .text.serial_enable_rx_irq
                0x0800560c       0x50 objs/serial_api.o
 .text.serial_disable_rx_irq
                0x0800565c       0x50 objs/serial_api.o
 .text.serial_enable_tx_irq
                0x080056ac       0x50 objs/serial_api.o
 .text.serial_disable_tx_irq
                0x080056fc       0x50 objs/serial_api.o
 .text.serial_read_timeout_signal
                0x0800574c       0xf4 objs/serial_api.o
                0x0800574c                serial_read_timeout_signal
 .text.serial_read_timeout
                0x08005840       0x28 objs/serial_api.o
                0x08005840                serial_read_timeout
 .text.serial_write
                0x08005868       0x82 objs/serial_api.o
                0x08005868                serial_write
 .text.rtc_save
                0x080058ea        0xe objs/port_stubs.o
                0x080058ea                rtc_save
 .text.HAL_GetTick
                0x080058f8       0x18 objs/port_stubs.o
                0x080058f8                HAL_GetTick
 .text.serial_write_direct
                0x08005910       0x5c objs/serial_api_stubs.o
                0x08005910                serial_write_direct
 .text.serial_flush
                0x0800596c       0x60 objs/serial_api_stubs.o
                0x0800596c                serial_flush
 .text.serial_buffer
                0x080059cc       0x1a objs/serial_api_stubs.o
                0x080059cc                serial_buffer
 .text.serial_line_mode
                0x080059e6       0x16 objs/serial_api_stubs.o
                0x080059e6                serial_line_mode
 .text.serial_power_on
                0x080059fc       0x28 objs/serial_api_stubs.o
                0x080059fc                serial_power_on
 .text.ITM_SendChar
                0x08005a24       0x4e objs/serial_wire_debug.o
 .text.ITM_SendStringSized
                0x08005a72       0x36 objs/serial_wire_debug.o
                0x08005a72                ITM_SendStringSized
 .text.HAL_Init
                0x08005aa8       0x30 objs/stm32l4xx_hal.o
                0x08005aa8                HAL_Init
 .text.HAL_MspInit
                0x08005ad8        0xe objs/stm32l4xx_hal.o
                0x08005ad8                HAL_MspInit
 *fill*         0x08005ae6        0x2 
 .text.HAL_InitTick
                0x08005ae8       0x78 objs/stm32l4xx_hal.o
                0x08005ae8                HAL_InitTick
 .text.HAL_IncTick
                0x08005b60       0x28 objs/stm32l4xx_hal.o
                0x08005b60                HAL_IncTick
 .text.__NVIC_SetPriorityGrouping
                0x08005b88       0x48 objs/stm32l4xx_hal_cortex.o
 .text.__NVIC_GetPriorityGrouping
                0x08005bd0       0x1c objs/stm32l4xx_hal_cortex.o
 .text.__NVIC_SetPriority
                0x08005bec       0x54 objs/stm32l4xx_hal_cortex.o
 .text.NVIC_EncodePriority
                0x08005c40       0x66 objs/stm32l4xx_hal_cortex.o
 *fill*         0x08005ca6        0x2 
 .text.SysTick_Config
                0x08005ca8       0x44 objs/stm32l4xx_hal_cortex.o
 .text.HAL_NVIC_SetPriorityGrouping
                0x08005cec       0x16 objs/stm32l4xx_hal_cortex.o
                0x08005cec                HAL_NVIC_SetPriorityGrouping
 .text.HAL_NVIC_SetPriority
                0x08005d02       0x38 objs/stm32l4xx_hal_cortex.o
                0x08005d02                HAL_NVIC_SetPriority
 .text.HAL_SYSTICK_Config
                0x08005d3a       0x18 objs/stm32l4xx_hal_cortex.o
                0x08005d3a                HAL_SYSTICK_Config
 *fill*         0x08005d52        0x2 
 .text.HAL_PWR_EnableBkUpAccess
                0x08005d54       0x20 objs/stm32l4xx_hal_pwr.o
                0x08005d54                HAL_PWR_EnableBkUpAccess
 .text.RTC_Bcd2ToByte
                0x08005d74       0x34 objs/stm32l4xx_hal_rtc.o
                0x08005d74                RTC_Bcd2ToByte
 .text.HAL_RTCEx_BKUPWrite
                0x08005da8       0x32 objs/stm32l4xx_hal_rtc_ex.o
                0x08005da8                HAL_RTCEx_BKUPWrite
 .text.HAL_RTCEx_BKUPRead
                0x08005dda       0x2c objs/stm32l4xx_hal_rtc_ex.o
                0x08005dda                HAL_RTCEx_BKUPRead
 *fill*         0x08005e06        0x2 
 .text.HAL_RCC_GetSysClockFreq
                0x08005e08      0x118 objs/stm32l4xx_hal_rcc.o
                0x08005e08                HAL_RCC_GetSysClockFreq
 .text.HAL_RCC_GetHCLKFreq
                0x08005f20       0x18 objs/stm32l4xx_hal_rcc.o
                0x08005f20                HAL_RCC_GetHCLKFreq
 .text.HAL_RCC_GetPCLK1Freq
                0x08005f38       0x2c objs/stm32l4xx_hal_rcc.o
                0x08005f38                HAL_RCC_GetPCLK1Freq
 .text.HAL_RCC_GetPCLK2Freq
                0x08005f64       0x2c objs/stm32l4xx_hal_rcc.o
                0x08005f64                HAL_RCC_GetPCLK2Freq
 .text.HAL_RCCEx_PeriphCLKConfig
                0x08005f90      0x430 objs/stm32l4xx_hal_rcc_ex.o
                0x08005f90                HAL_RCCEx_PeriphCLKConfig
 .text.RCCEx_PLLSAI1_Config
                0x080063c0      0x1e4 objs/stm32l4xx_hal_rcc_ex.o
 .text.HAL_UART_Init
                0x080065a4       0x9c objs/stm32l4xx_hal_uart.o
                0x080065a4                HAL_UART_Init
 .text.HAL_UART_MspInit
                0x08006640       0x14 objs/stm32l4xx_hal_uart.o
                0x08006640                HAL_UART_MspInit
 .text.HAL_UART_Transmit
                0x08006654      0x126 objs/stm32l4xx_hal_uart.o
                0x08006654                HAL_UART_Transmit
 *fill*         0x0800677a        0x2 
 .text.UART_SetConfig
                0x0800677c      0x4b8 objs/stm32l4xx_hal_uart.o
                0x0800677c                UART_SetConfig
 .text.UART_AdvFeatureConfig
                0x08006c34      0x144 objs/stm32l4xx_hal_uart.o
                0x08006c34                UART_AdvFeatureConfig
 .text.UART_CheckIdleState
                0x08006d78       0x92 objs/stm32l4xx_hal_uart.o
                0x08006d78                UART_CheckIdleState
 .text.UART_WaitOnFlagUntilTimeout
                0x08006e0a       0xf8 objs/stm32l4xx_hal_uart.o
                0x08006e0a                UART_WaitOnFlagUntilTimeout
 .text.HAL_UARTEx_DisableClockStopMode
                0x08006f02       0x44 objs/stm32l4xx_hal_uart_ex.o
                0x08006f02                HAL_UARTEx_DisableClockStopMode
 .text.HAL_UARTEx_EnableStopMode
                0x08006f46       0x44 objs/stm32l4xx_hal_uart_ex.o
                0x08006f46                HAL_UARTEx_EnableStopMode
 .text.HAL_UARTEx_DisableStopMode
                0x08006f8a       0x44 objs/stm32l4xx_hal_uart_ex.o
                0x08006f8a                HAL_UARTEx_DisableStopMode
 .text.gpio_write
                0x08006fce       0x32 objs/gps_quectel.o
 .text.gps_power_on
                0x08007000       0x28 objs/gps_quectel.o
                0x08007000                gps_power_on
 .text.gps_init
                0x08007028       0x84 objs/gps_quectel.o
                0x08007028                gps_init
 .text.gps_pin_setup
                0x080070ac       0x58 objs/gps_quectel.o
                0x080070ac                gps_pin_setup
 .text.gps_send_command
                0x08007104      0x1e8 objs/gps_quectel.o
                0x08007104                gps_send_command
 .text.gps_reset
                0x080072ec       0x70 objs/gps_quectel.o
                0x080072ec                gps_reset
 .text.gps_highres_mode
                0x0800735c       0x54 objs/gps_quectel.o
                0x0800735c                gps_highres_mode
 .text.nmea_init
                0x080073b0       0x1a objs/nmea.o
                0x080073b0                nmea_init
 .text.nmea_fusedata
                0x080073ca      0x1da objs/nmea.o
                0x080073ca                nmea_fusedata
 *fill*         0x080075a4        0x4 
 .text.nmea_parsedata
                0x080075a8      0x724 objs/nmea.o
                0x080075a8                nmea_parsedata
 .text.nmea_digit2dec
                0x08007ccc       0x26 objs/nmea.o
                0x08007ccc                nmea_digit2dec
 .text.flash_store_lock
                0x08007cf2       0x18 objs/gps_serial_test.o
                0x08007cf2                flash_store_lock
 .text.flash_store_unlock
                0x08007d0a       0x14 objs/gps_serial_test.o
                0x08007d0a                flash_store_unlock
 .text.flash_store_partial_okay
                0x08007d1e       0x18 objs/gps_serial_test.o
                0x08007d1e                flash_store_partial_okay
 .text.flash_store_write
                0x08007d36       0x1a objs/gps_serial_test.o
                0x08007d36                flash_store_write
 .text.gps_test_serial_communication
                0x08007d50      0x30c objs/gps_serial_test.o
                0x08007d50                gps_test_serial_communication
 .text.test_gps_thread_subset
                0x0800805c      0x168 objs/gps_serial_test.o
                0x0800805c                test_gps_thread_subset
 .text.main     0x080081c4       0x74 objs/gps_serial_test.o
                0x080081c4                main
 .text          0x08008238      0x378 /usr/lib/gcc/arm-none-eabi/13.2.1/thumb/v7e-m+fp/hard/libgcc.a(_arm_addsubdf3.o)
                0x08008238                __aeabi_drsub
                0x08008240                __aeabi_dsub
                0x08008240                __subdf3
                0x08008244                __adddf3
                0x08008244                __aeabi_dadd
                0x080084bc                __aeabi_ui2d
                0x080084bc                __floatunsidf
                0x080084dc                __floatsidf
                0x080084dc                __aeabi_i2d
                0x08008500                __extendsfdf2
                0x08008500                __aeabi_f2d
                0x08008544                __aeabi_ul2d
                0x08008544                __floatundidf
                0x08008554                __floatdidf
                0x08008554                __aeabi_l2d
 .text          0x080085b0      0x424 /usr/lib/gcc/arm-none-eabi/13.2.1/thumb/v7e-m+fp/hard/libgcc.a(_arm_muldivdf3.o)
                0x080085b0                __aeabi_dmul
                0x080085b0                __muldf3
                0x08008804                __aeabi_ddiv
                0x08008804                __divdf3
 .text          0x080089d4       0xa0 /usr/lib/gcc/arm-none-eabi/13.2.1/thumb/v7e-m+fp/hard/libgcc.a(_arm_truncdfsf2.o)
                0x080089d4                __aeabi_d2f
                0x080089d4                __truncdfsf2
 .text          0x08008a74       0x30 /usr/lib/gcc/arm-none-eabi/13.2.1/thumb/v7e-m+fp/hard/libgcc.a(_aeabi_uldivmod.o)
                0x08008a74                __aeabi_uldivmod
 .text          0x08008aa4      0x2bc /usr/lib/gcc/arm-none-eabi/13.2.1/thumb/v7e-m+fp/hard/libgcc.a(_udivmoddi4.o)
                0x08008aa4                __udivmoddi4
 .text          0x08008d60        0x4 /usr/lib/gcc/arm-none-eabi/13.2.1/thumb/v7e-m+fp/hard/libgcc.a(_dvmd_tls.o)
                0x08008d60                __aeabi_ldiv0
                0x08008d60                __aeabi_idiv0
 .text          0x08008d64       0x3c /usr/lib/gcc/arm-none-eabi/13.2.1/../../../arm-none-eabi/lib/thumb/v7e-m+fp/hard/libm.a(libm_a-sf_trunc.o)
                0x08008d64                truncf
 *(.init)
 *(.fini)
 *crtbegin.o(.ctors)
 *crtbegin?.o(.ctors)
 *(EXCLUDE_FILE(*crtend.o *crtend?.o) .ctors)
 *(SORT_BY_NAME(.ctors.*))
 *(.ctors)
 *crtbegin.o(.dtors)
 *crtbegin?.o(.dtors)
 *(EXCLUDE_FILE(*crtend.o *crtend?.o) .dtors)
 *(SORT_BY_NAME(.dtors.*))
 *(.dtors)
 *(.rodata*)
 .rodata.os_maxtaskrun
                0x08008da0        0x2 objs/RTX_Conf_CM.o
                0x08008da0                os_maxtaskrun
 *fill*         0x08008da2        0x2 
 .rodata.os_stackinfo
                0x08008da4        0x4 objs/RTX_Conf_CM.o
                0x08008da4                os_stackinfo
 .rodata.os_rrobin
                0x08008da8        0x4 objs/RTX_Conf_CM.o
                0x08008da8                os_rrobin
 .rodata.os_trv
                0x08008dac        0x4 objs/RTX_Conf_CM.o
                0x08008dac                os_trv
 .rodata.os_flags
                0x08008db0        0x1 objs/RTX_Conf_CM.o
                0x08008db0                os_flags
 *fill*         0x08008db1        0x3 
 .rodata.os_clockrate
                0x08008db4        0x4 objs/RTX_Conf_CM.o
                0x08008db4                os_clockrate
 .rodata.mp_tcb_size
                0x08008db8        0x2 objs/RTX_Conf_CM.o
                0x08008db8                mp_tcb_size
 *fill*         0x08008dba        0x2 
 .rodata.mp_stk_size
                0x08008dbc        0x4 objs/RTX_Conf_CM.o
                0x08008dbc                mp_stk_size
 .rodata.os_stack_sz
                0x08008dc0        0x4 objs/RTX_Conf_CM.o
                0x08008dc0                os_stack_sz
 .rodata.os_fifo_size
                0x08008dc4        0x1 objs/RTX_Conf_CM.o
                0x08008dc4                os_fifo_size
 *fill*         0x08008dc5        0x3 
 .rodata.os_thread_def_osTimerThread
                0x08008dc8       0x10 objs/RTX_Conf_CM.o
                0x08008dc8                os_thread_def_osTimerThread
 .rodata.os_messageQ_def_osTimerMessageQ
                0x08008dd8        0x8 objs/RTX_Conf_CM.o
                0x08008dd8                os_messageQ_def_osTimerMessageQ
 .rodata.mp_tmr_size
                0x08008de0        0x2 objs/RTX_Conf_CM.o
                0x08008de0                mp_tmr_size
 *fill*         0x08008de2        0x2 
 .rodata.APBPrescTable
                0x08008de4        0x8 objs/system_stm32l4xx.o
                0x08008de4                APBPrescTable
 .rodata.MSIRangeTable
                0x08008dec       0x30 objs/system_stm32l4xx.o
                0x08008dec                MSIRangeTable
 .rodata        0x08008e1c       0x94 objs/board_fault.o
 .rodata        0x08008eb0       0x63 objs/printf.o
 *fill*         0x08008f13        0x1 
 .rodata.days_per_month
                0x08008f14       0x30 objs/datetime.o
                0x08008f14                days_per_month
 .rodata.days_in_month
                0x08008f44       0x1a objs/datetime.o
                0x08008f44                days_in_month
 *fill*         0x08008f5e        0x2 
 .rodata        0x08008f60       0x30 objs/datetime.o
 .rodata        0x08008f90      0x1a4 objs/event.o
 .rodata        0x08009134       0x70 objs/system_file.o
 .rodata        0x080091a4       0x5f objs/gpio_api.o
 *fill*         0x08009203        0x1 
 .rodata.PinMap_UART_TX
                0x08009204       0x54 objs/PeripheralPins.o
                0x08009204                PinMap_UART_TX
 .rodata.PinMap_UART_RX
                0x08009258       0x60 objs/PeripheralPins.o
                0x08009258                PinMap_UART_RX
 .rodata.ll_pin_defines
                0x080092b8       0x40 objs/pinmap.o
                0x080092b8                ll_pin_defines
 .rodata        0x080092f8       0x26 objs/pinmap.o
 *fill*         0x0800931e        0x2 
 .rodata        0x08009320       0x11 objs/mbed_pinmap_common.o
 *fill*         0x08009331        0x3 
 .rodata        0x08009334       0xe6 objs/serial_api.o
 *fill*         0x0800941a        0x2 
 .rodata        0x0800941c       0xe9 objs/gps_quectel.o
 *fill*         0x08009505        0x3 
 .rodata        0x08009508       0x2e objs/nmea.o
 *fill*         0x08009536        0x2 
 .rodata        0x08009538      0x614 objs/gps_serial_test.o
 *(.eh_frame*)

.glue_7         0x08009b4c        0x0
 .glue_7        0x08009b4c        0x0 linker stubs

.glue_7t        0x08009b4c        0x0
 .glue_7t       0x08009b4c        0x0 linker stubs

.vfp11_veneer   0x08009b4c        0x0
 .vfp11_veneer  0x08009b4c        0x0 linker stubs

.v4_bx          0x08009b4c        0x0
 .v4_bx         0x08009b4c        0x0 linker stubs

.iplt           0x08009b4c        0x0
 .iplt          0x08009b4c        0x0 objs/startup_stm32l431xx.o

.sram.text      0x08009b4c       0xce
 .sram.text     0x08009b4c       0xce objs/libc.o
                0x08009b4c                memcpy

.ARM.extab
 *(.ARM.extab* .gnu.linkonce.armextab.*)
                0x08009c1a                        __exidx_start = .

.ARM.exidx      0x08009c1c        0x8
 *(.ARM.exidx* .gnu.linkonce.armexidx.*)
 .ARM.exidx     0x08009c1c        0x8 objs/HAL_CM4.o
                                 0x48 (size before relaxing)
 .ARM.exidx     0x08009c24        0x0 /usr/lib/gcc/arm-none-eabi/13.2.1/thumb/v7e-m+fp/hard/libgcc.a(_udivmoddi4.o)
                                  0x8 (size before relaxing)
                0x08009c24                        __exidx_end = .
                0x08009c24                        __etext = .
                0x08009c24                        _sidata = .

.rel.dyn        0x08009c24        0x0
 .rel.iplt      0x08009c24        0x0 objs/startup_stm32l431xx.o

.data           0x20000190       0xa0 load address 0x08009c24
                0x20000190                        __data_start__ = .
                0x20000190                        _sdata = .
 *(vtable)
 *(.data*)
 .data.os_thread_def_main
                0x20000190       0x10 objs/RTX_Conf_CM.o
                0x20000190                os_thread_def_main
 .data.SystemCoreClock
                0x200001a0        0x4 objs/system_stm32l4xx.o
                0x200001a0                SystemCoreClock
 .data.lower_digits
                0x200001a4        0x4 objs/printf.o
 .data.upper_digits
                0x200001a8        0x4 objs/printf.o
 .data.level_names
                0x200001ac       0x10 objs/event.o
 .data.source_names
                0x200001bc       0x60 objs/event.o
 .data.event_show
                0x2000021c        0x1 objs/event.o
                0x2000021c                event_show
 *fill*         0x2000021d        0x3 
 .data.sys_reg_file_ptr
                0x20000220        0x4 objs/system_file.o
                0x20000220                sys_reg_file_ptr
 .data.uwTickPrio
                0x20000224        0x4 objs/stm32l4xx_hal.o
                0x20000224                uwTickPrio
 .data.uwTickFreq
                0x20000228        0x1 objs/stm32l4xx_hal.o
                0x20000228                uwTickFreq
                0x20000230                        . = ALIGN (0x8)
 *fill*         0x20000229        0x7 
                [!provide]                        PROVIDE (__preinit_array_start = .)
 *(.preinit_array)
                [!provide]                        PROVIDE (__preinit_array_end = .)
                0x20000230                        . = ALIGN (0x8)
                [!provide]                        PROVIDE (__init_array_start = .)
 *(SORT_BY_NAME(.init_array.*))
 *(.init_array)
                [!provide]                        PROVIDE (__init_array_end = .)
                0x20000230                        . = ALIGN (0x8)
                [!provide]                        PROVIDE (__fini_array_start = .)
 *(SORT_BY_NAME(.fini_array.*))
 *(.fini_array)
                [!provide]                        PROVIDE (__fini_array_end = .)
 *(.jcr*)
                0x20000230                        . = ALIGN (0x8)
                0x20000230                        __data_end__ = .
                0x20000230                        _edata = .

.igot.plt       0x20000230        0x0 load address 0x08009cc4
 .igot.plt      0x20000230        0x0 objs/startup_stm32l431xx.o

.uninitialized  0x20000230       0x10 load address 0x08009cc4
                0x20000240                        . = ALIGN (0x20)
 *fill*         0x20000230       0x10 
                0x20000240                        __uninitialized_start = .
 *(.uninitialized)
 *(.keep.uninitialized)
                0x20000240                        . = ALIGN (0x20)
                0x20000240                        __uninitialized_end = .

.bss            0x20000240     0x4c70 load address 0x08009cd8
                0x20000240                        . = ALIGN (0x8)
                0x20000240                        __bss_start__ = .
                0x20000240                        _sbss = .
 *(.bss*)
 .bss.mp_tcb    0x20000240      0x1cc objs/RTX_Conf_CM.o
                0x20000240                mp_tcb
 *fill*         0x2000040c        0x4 
 .bss.mp_stk    0x20000410     0x1eb0 objs/RTX_Conf_CM.o
                0x20000410                mp_stk
 .bss.os_stack_mem
                0x200022c0      0x598 objs/RTX_Conf_CM.o
                0x200022c0                os_stack_mem
 .bss.os_fifo   0x20002858       0x84 objs/RTX_Conf_CM.o
                0x20002858                os_fifo
 .bss.os_active_TCB
                0x200028dc       0x20 objs/RTX_Conf_CM.o
                0x200028dc                os_active_TCB
 .bss.osThreadId_osTimerThread
                0x200028fc        0x4 objs/RTX_Conf_CM.o
                0x200028fc                osThreadId_osTimerThread
 .bss.os_messageQ_q_osTimerMessageQ
                0x20002900       0x30 objs/RTX_Conf_CM.o
                0x20002900                os_messageQ_q_osTimerMessageQ
 .bss.osMessageQId_osTimerMessageQ
                0x20002930        0x4 objs/RTX_Conf_CM.o
                0x20002930                osMessageQId_osTimerMessageQ
 .bss.m_tmr     0x20002934        0x4 objs/RTX_Conf_CM.o
                0x20002934                m_tmr
 .bss.os_initialized
                0x20002938        0x1 objs/rt_CMSIS.o
                0x20002938                os_initialized
 .bss.os_running
                0x20002939        0x1 objs/rt_CMSIS.o
                0x20002939                os_running
 *fill*         0x2000293a        0x2 
 .bss.os_timer_head
                0x2000293c        0x4 objs/rt_CMSIS.o
                0x2000293c                os_timer_head
 .bss.os_rdy    0x20002940       0x18 objs/rt_List.o
                0x20002940                os_rdy
 .bss.os_dly    0x20002958       0x18 objs/rt_List.o
                0x20002958                os_dly
 .bss.os_robin  0x20002970        0x8 objs/rt_Robin.o
                0x20002970                os_robin
 .bss.os_tick_irqn
                0x20002978        0x4 objs/rt_System.o
                0x20002978                os_tick_irqn
 .bss.os_lock   0x2000297c        0x1 objs/rt_System.o
 .bss.os_psh_flag
                0x2000297d        0x1 objs/rt_System.o
 *fill*         0x2000297e        0x2 
 .bss.os_tsk    0x20002980        0x8 objs/rt_Task.o
                0x20002980                os_tsk
 .bss.os_idle_TCB
                0x20002988       0x38 objs/rt_Task.o
                0x20002988                os_idle_TCB
 .bss.os_time   0x200029c0        0x4 objs/rt_Time.o
                0x200029c0                os_time
 .bss.board_initted
                0x200029c4        0x1 objs/start.o
                0x200029c4                board_initted
 *fill*         0x200029c5        0x3 
 .bss.fault_r0  0x200029c8        0x4 objs/board_fault.o
                0x200029c8                fault_r0
 .bss.fault_r1  0x200029cc        0x4 objs/board_fault.o
                0x200029cc                fault_r1
 .bss.fault_r2  0x200029d0        0x4 objs/board_fault.o
                0x200029d0                fault_r2
 .bss.fault_r3  0x200029d4        0x4 objs/board_fault.o
                0x200029d4                fault_r3
 .bss.fault_r12
                0x200029d8        0x4 objs/board_fault.o
                0x200029d8                fault_r12
 .bss.fault_lr  0x200029dc        0x4 objs/board_fault.o
                0x200029dc                fault_lr
 .bss.fault_pc  0x200029e0        0x4 objs/board_fault.o
                0x200029e0                fault_pc
 .bss.fault_psr
                0x200029e4        0x4 objs/board_fault.o
                0x200029e4                fault_psr
 .bss.board_fault
                0x200029e8        0x1 objs/board_fault.o
                0x200029e8                board_fault
 *fill*         0x200029e9        0x3 
 .bss.printf_mutex
                0x200029ec        0x4 objs/printf.o
                0x200029ec                printf_mutex
 .bss.event_display_mutex
                0x200029f0        0x4 objs/event.o
                0x200029f0                event_display_mutex
 .bss.event_buffer
                0x200029f4       0x80 objs/event.o
                0x200029f4                event_buffer
 .bss.event_time
                0x20002a74        0x4 objs/event.o
                0x20002a74                event_time
 .bss.event_in  0x20002a78        0x1 objs/event.o
                0x20002a78                event_in
 .bss.event_out
                0x20002a79        0x1 objs/event.o
                0x20002a79                event_out
 *fill*         0x20002a7a        0x2 
 .bss.event_entries
                0x20002a7c     0x1600 objs/event.o
                0x20002a7c                event_entries
 .bss.sys_reg_file_data
                0x2000407c       0x18 objs/system_file.o
 .bss.hrtc_backup
                0x20004094       0x24 objs/system_file.o
 .bss.backup_domain_initialized
                0x200040b8        0x1 objs/system_file.o
 *fill*         0x200040b9        0x3 
 .bss.stdio_uart_inited
                0x200040bc        0x4 objs/serial_api.o
                0x200040bc                stdio_uart_inited
 .bss.stdio_uart
                0x200040c0       0x74 objs/serial_api.o
                0x200040c0                stdio_uart
 .bss.serial_rx_buffers
                0x20004134      0x300 objs/serial_api.o
 .bss.serial_tx_buffers
                0x20004434      0x300 objs/serial_api.o
 .bss.serial_objects
                0x20004734        0xc objs/serial_api.o
                0x20004734                serial_objects
 .bss.uart_handlers
                0x20004740      0x18c objs/serial_device.o
                0x20004740                uart_handlers
 .bss.console_uart
                0x200048cc       0x74 objs/serial_api_stubs.o
                0x200048cc                console_uart
 .bss.uwTick    0x20004940        0x4 objs/stm32l4xx_hal.o
                0x20004940                uwTick
 .bss.gps_en_pin
                0x20004944       0x1c objs/gps_quectel.o
                0x20004944                gps_en_pin
 .bss.gps_vbckp_pin
                0x20004960       0x1c objs/gps_quectel.o
                0x20004960                gps_vbckp_pin
 .bss.gps_reset_pin
                0x2000497c       0x1c objs/gps_quectel.o
                0x2000497c                gps_reset_pin
 .bss.gps_pps_pin
                0x20004998       0x1c objs/gps_quectel.o
                0x20004998                gps_pps_pin
 .bss.gps_initted
                0x200049b4        0x1 objs/gps_quectel.o
                0x200049b4                gps_initted
 .bss.gps_pin_initted
                0x200049b5        0x1 objs/gps_quectel.o
                0x200049b5                gps_pin_initted
 *fill*         0x200049b6        0x2 
 .bss.gps_port  0x200049b8       0x74 objs/gps_quectel.o
                0x200049b8                gps_port
 .bss.gps_tx_buffer
                0x20004a2c       0x20 objs/gps_quectel.o
                0x20004a2c                gps_tx_buffer
 .bss.gps_rx_buffer
                0x20004a4c       0x80 objs/gps_quectel.o
                0x20004a4c                gps_rx_buffer
 .bss.settings  0x20004acc      0x18c objs/settings.o
                0x20004acc                settings
 .bss.gps_data  0x20004c58       0x50 objs/gps_serial_test.o
                0x20004c58                gps_data
 .bss.gps_loc   0x20004ca8      0x1b0 objs/gps_serial_test.o
                0x20004ca8                gps_loc
 .bss.gps_show_bytes
                0x20004e58        0x1 objs/gps_serial_test.o
                0x20004e58                gps_show_bytes
 *fill*         0x20004e59        0x3 
 .bss.event_partition
                0x20004e5c       0x50 objs/gps_serial_test.o
                0x20004e5c                event_partition
 *(COMMON)
                0x20004eb0                        . = ALIGN (0x8)
 *fill*         0x20004eac        0x4 
                0x20004eb0                        __bss_end__ = .
                0x20004eb0                        _ebss = .

.heap           0x20004eb0     0xad50
                0x20004eb0                        __end__ = .
                [!provide]                        PROVIDE (end = .)
 *(.heap*)
                0x2000fc00                        . = ((ORIGIN (RAM) + LENGTH (RAM)) - 0x400)
 *fill*         0x20004eb0     0xad50 
                0x2000fc00                        __HeapLimit = .

.stack_dummy
 *(.stack*)
                0x20010000                        __StackTop = (ORIGIN (RAM) + LENGTH (RAM))
                0x20010000                        _estack = __StackTop
                0x2000fc00                        __StackLimit = (__StackTop - 0x400)
                [!provide]                        PROVIDE (__stack = __StackTop)
                0x00000001                        ASSERT ((__StackLimit >= __HeapLimit), region RAM overflowed with stack)
OUTPUT(images/gps_serial_test.elf elf32-littlearm)
LOAD linker stubs

.ARM.attributes
                0x00000000       0x35
 .ARM.attributes
                0x00000000       0x21 objs/startup_stm32l431xx.o
 .ARM.attributes
                0x00000021       0x20 objs/HAL_CM4.o
 .ARM.attributes
                0x00000041       0x34 objs/HAL_CM.o
 .ARM.attributes
                0x00000075       0x34 objs/RTX_Conf_CM.o
 .ARM.attributes
                0x000000a9       0x34 objs/rt_CMSIS.o
 .ARM.attributes
                0x000000dd       0x34 objs/rt_Event.o
 .ARM.attributes
                0x00000111       0x34 objs/rt_List.o
 .ARM.attributes
                0x00000145       0x34 objs/rt_Mailbox.o
 .ARM.attributes
                0x00000179       0x34 objs/rt_MemBox.o
 .ARM.attributes
                0x000001ad       0x34 objs/rt_Memory.o
 .ARM.attributes
                0x000001e1       0x34 objs/rt_Mutex.o
 .ARM.attributes
                0x00000215       0x34 objs/rt_Robin.o
 .ARM.attributes
                0x00000249       0x34 objs/rt_Semaphore.o
 .ARM.attributes
                0x0000027d       0x34 objs/rt_System.o
 .ARM.attributes
                0x000002b1       0x34 objs/rt_Task.o
 .ARM.attributes
                0x000002e5       0x34 objs/rt_Time.o
 .ARM.attributes
                0x00000319       0x34 objs/system_stm32l4xx.o
 .ARM.attributes
                0x0000034d       0x34 objs/start.o
 .ARM.attributes
                0x00000381       0x34 objs/board_fault.o
 .ARM.attributes
                0x000003b5       0x34 objs/bits.o
 .ARM.attributes
                0x000003e9       0x34 objs/printf.o
 .ARM.attributes
                0x0000041d       0x34 objs/libc.o
 .ARM.attributes
                0x00000451       0x34 objs/rtc_api.o
 .ARM.attributes
                0x00000485       0x34 objs/os_idle.o
 .ARM.attributes
                0x000004b9       0x34 objs/datetime.o
 .ARM.attributes
                0x000004ed       0x34 objs/console.o
 .ARM.attributes
                0x00000521       0x34 objs/event.o
 .ARM.attributes
                0x00000555       0x34 objs/system_file.o
 .ARM.attributes
                0x00000589       0x34 objs/gpio.o
 .ARM.attributes
                0x000005bd       0x34 objs/gpio_api.o
 .ARM.attributes
                0x000005f1       0x34 objs/PeripheralPins.o
 .ARM.attributes
                0x00000625       0x34 objs/pinmap.o
 .ARM.attributes
                0x00000659       0x34 objs/mbed_pinmap_common.o
 .ARM.attributes
                0x0000068d       0x34 objs/serial_api.o
 .ARM.attributes
                0x000006c1       0x34 objs/serial_device.o
 .ARM.attributes
                0x000006f5       0x34 objs/port_stubs.o
 .ARM.attributes
                0x00000729       0x34 objs/serial_api_stubs.o
 .ARM.attributes
                0x0000075d       0x34 objs/serial_wire_debug.o
 .ARM.attributes
                0x00000791       0x34 objs/stm32l4xx_hal.o
 .ARM.attributes
                0x000007c5       0x34 objs/stm32l4xx_hal_cortex.o
 .ARM.attributes
                0x000007f9       0x34 objs/stm32l4xx_hal_pwr.o
 .ARM.attributes
                0x0000082d       0x34 objs/stm32l4xx_hal_rtc.o
 .ARM.attributes
                0x00000861       0x34 objs/stm32l4xx_hal_rtc_ex.o
 .ARM.attributes
                0x00000895       0x34 objs/stm32l4xx_hal_rcc.o
 .ARM.attributes
                0x000008c9       0x34 objs/stm32l4xx_hal_rcc_ex.o
 .ARM.attributes
                0x000008fd       0x34 objs/stm32l4xx_hal_uart.o
 .ARM.attributes
                0x00000931       0x34 objs/stm32l4xx_hal_uart_ex.o
 .ARM.attributes
                0x00000965       0x34 objs/gps_quectel.o
 .ARM.attributes
                0x00000999       0x34 objs/nmea.o
 .ARM.attributes
                0x000009cd       0x34 objs/settings.o
 .ARM.attributes
                0x00000a01       0x34 objs/gps_serial_test.o
 .ARM.attributes
                0x00000a35       0x22 /usr/lib/gcc/arm-none-eabi/13.2.1/thumb/v7e-m+fp/hard/libgcc.a(_arm_addsubdf3.o)
 .ARM.attributes
                0x00000a57       0x22 /usr/lib/gcc/arm-none-eabi/13.2.1/thumb/v7e-m+fp/hard/libgcc.a(_arm_muldivdf3.o)
 .ARM.attributes
                0x00000a79       0x22 /usr/lib/gcc/arm-none-eabi/13.2.1/thumb/v7e-m+fp/hard/libgcc.a(_arm_truncdfsf2.o)
 .ARM.attributes
                0x00000a9b       0x22 /usr/lib/gcc/arm-none-eabi/13.2.1/thumb/v7e-m+fp/hard/libgcc.a(_aeabi_uldivmod.o)
 .ARM.attributes
                0x00000abd       0x34 /usr/lib/gcc/arm-none-eabi/13.2.1/thumb/v7e-m+fp/hard/libgcc.a(_udivmoddi4.o)
 .ARM.attributes
                0x00000af1       0x22 /usr/lib/gcc/arm-none-eabi/13.2.1/thumb/v7e-m+fp/hard/libgcc.a(_dvmd_tls.o)
 .ARM.attributes
                0x00000b13       0x34 /usr/lib/gcc/arm-none-eabi/13.2.1/../../../arm-none-eabi/lib/thumb/v7e-m+fp/hard/libm.a(libm_a-sf_trunc.o)

.svc_table      0x00000000        0x4
 .svc_table     0x00000000        0x4 objs/SVC_Table.o
                0x00000000                SVC_Count
                0x00000000                SVC_Table

.comment        0x00000000       0x26
 .comment       0x00000000       0x26 objs/HAL_CM.o
                                 0x27 (size before relaxing)
 .comment       0x00000026       0x27 objs/RTX_Conf_CM.o
 .comment       0x00000026       0x27 objs/rt_CMSIS.o
 .comment       0x00000026       0x27 objs/rt_Event.o
 .comment       0x00000026       0x27 objs/rt_List.o
 .comment       0x00000026       0x27 objs/rt_Mailbox.o
 .comment       0x00000026       0x27 objs/rt_MemBox.o
 .comment       0x00000026       0x27 objs/rt_Memory.o
 .comment       0x00000026       0x27 objs/rt_Mutex.o
 .comment       0x00000026       0x27 objs/rt_Robin.o
 .comment       0x00000026       0x27 objs/rt_Semaphore.o
 .comment       0x00000026       0x27 objs/rt_System.o
 .comment       0x00000026       0x27 objs/rt_Task.o
 .comment       0x00000026       0x27 objs/rt_Time.o
 .comment       0x00000026       0x27 objs/system_stm32l4xx.o
 .comment       0x00000026       0x27 objs/start.o
 .comment       0x00000026       0x27 objs/board_fault.o
 .comment       0x00000026       0x27 objs/bits.o
 .comment       0x00000026       0x27 objs/printf.o
 .comment       0x00000026       0x27 objs/libc.o
 .comment       0x00000026       0x27 objs/rtc_api.o
 .comment       0x00000026       0x27 objs/os_idle.o
 .comment       0x00000026       0x27 objs/datetime.o
 .comment       0x00000026       0x27 objs/console.o
 .comment       0x00000026       0x27 objs/event.o
 .comment       0x00000026       0x27 objs/system_file.o
 .comment       0x00000026       0x27 objs/gpio.o
 .comment       0x00000026       0x27 objs/gpio_api.o
 .comment       0x00000026       0x27 objs/PeripheralPins.o
 .comment       0x00000026       0x27 objs/pinmap.o
 .comment       0x00000026       0x27 objs/mbed_pinmap_common.o
 .comment       0x00000026       0x27 objs/serial_api.o
 .comment       0x00000026       0x27 objs/serial_device.o
 .comment       0x00000026       0x27 objs/port_stubs.o
 .comment       0x00000026       0x27 objs/serial_api_stubs.o
 .comment       0x00000026       0x27 objs/serial_wire_debug.o
 .comment       0x00000026       0x27 objs/stm32l4xx_hal.o
 .comment       0x00000026       0x27 objs/stm32l4xx_hal_cortex.o
 .comment       0x00000026       0x27 objs/stm32l4xx_hal_pwr.o
 .comment       0x00000026       0x27 objs/stm32l4xx_hal_rtc.o
 .comment       0x00000026       0x27 objs/stm32l4xx_hal_rtc_ex.o
 .comment       0x00000026       0x27 objs/stm32l4xx_hal_rcc.o
 .comment       0x00000026       0x27 objs/stm32l4xx_hal_rcc_ex.o
 .comment       0x00000026       0x27 objs/stm32l4xx_hal_uart.o
 .comment       0x00000026       0x27 objs/stm32l4xx_hal_uart_ex.o
 .comment       0x00000026       0x27 objs/gps_quectel.o
 .comment       0x00000026       0x27 objs/nmea.o
 .comment       0x00000026       0x27 objs/settings.o
 .comment       0x00000026       0x27 objs/gps_serial_test.o
 .comment       0x00000026       0x27 /usr/lib/gcc/arm-none-eabi/13.2.1/thumb/v7e-m+fp/hard/libgcc.a(_udivmoddi4.o)
 .comment       0x00000026       0x27 /usr/lib/gcc/arm-none-eabi/13.2.1/../../../arm-none-eabi/lib/thumb/v7e-m+fp/hard/libm.a(libm_a-sf_trunc.o)

.debug_line     0x00000000    0x10f1d
 .debug_line    0x00000000       0x79 objs/startup_stm32l431xx.o
 .debug_line    0x00000079       0xeb objs/HAL_CM4.o
 .debug_line    0x00000164      0x17f objs/HAL_CM.o
 .debug_line    0x000002e3       0xb0 objs/RTX_Conf_CM.o
 .debug_line    0x00000393     0x1cdc objs/rt_CMSIS.o
 .debug_line    0x0000206f      0x23c objs/rt_Event.o
 .debug_line    0x000022ab      0x437 objs/rt_List.o
 .debug_line    0x000026e2      0x3f0 objs/rt_Mailbox.o
 .debug_line    0x00002ad2      0x1b1 objs/rt_MemBox.o
 .debug_line    0x00002c83      0x18c objs/rt_Memory.o
 .debug_line    0x00002e0f      0x2fd objs/rt_Mutex.o
 .debug_line    0x0000310c       0xec objs/rt_Robin.o
 .debug_line    0x000031f8      0x1e1 objs/rt_Semaphore.o
 .debug_line    0x000033d9      0x3e8 objs/rt_System.o
 .debug_line    0x000037c1      0x4d9 objs/rt_Task.o
 .debug_line    0x00003c9a       0xfb objs/rt_Time.o
 .debug_line    0x00003d95      0x1b1 objs/system_stm32l4xx.o
 .debug_line    0x00003f46      0x193 objs/start.o
 .debug_line    0x000040d9      0x342 objs/board_fault.o
 .debug_line    0x0000441b      0x23f objs/bits.o
 .debug_line    0x0000465a      0x785 objs/printf.o
 .debug_line    0x00004ddf      0x595 objs/libc.o
 .debug_line    0x00005374      0x495 objs/rtc_api.o
 .debug_line    0x00005809       0xd3 objs/os_idle.o
 .debug_line    0x000058dc      0x323 objs/datetime.o
 .debug_line    0x00005bff      0x25c objs/console.o
 .debug_line    0x00005e5b      0x777 objs/event.o
 .debug_line    0x000065d2      0x366 objs/system_file.o
 .debug_line    0x00006938      0x1e8 objs/gpio.o
 .debug_line    0x00006b20      0x2ce objs/gpio_api.o
 .debug_line    0x00006dee       0xd8 objs/PeripheralPins.o
 .debug_line    0x00006ec6      0x51e objs/pinmap.o
 .debug_line    0x000073e4      0x36f objs/mbed_pinmap_common.o
 .debug_line    0x00007753      0xa28 objs/serial_api.o
 .debug_line    0x0000817b      0xaa3 objs/serial_device.o
 .debug_line    0x00008c1e      0x212 objs/port_stubs.o
 .debug_line    0x00008e30      0x4b3 objs/serial_api_stubs.o
 .debug_line    0x000092e3      0x1b3 objs/serial_wire_debug.o
 .debug_line    0x00009496      0x525 objs/stm32l4xx_hal.o
 .debug_line    0x000099bb      0x676 objs/stm32l4xx_hal_cortex.o
 .debug_line    0x0000a031      0x2ed objs/stm32l4xx_hal_pwr.o
 .debug_line    0x0000a31e      0xb87 objs/stm32l4xx_hal_rtc.o
 .debug_line    0x0000aea5      0xca4 objs/stm32l4xx_hal_rtc_ex.o
 .debug_line    0x0000bb49      0xabf objs/stm32l4xx_hal_rcc.o
 .debug_line    0x0000c608      0xdfc objs/stm32l4xx_hal_rcc_ex.o
 .debug_line    0x0000d404     0x16fb objs/stm32l4xx_hal_uart.o
 .debug_line    0x0000eaff      0x4ba objs/stm32l4xx_hal_uart_ex.o
 .debug_line    0x0000efb9      0x5a4 objs/gps_quectel.o
 .debug_line    0x0000f55d      0x568 objs/nmea.o
 .debug_line    0x0000fac5      0x44a objs/settings.o
 .debug_line    0x0000ff0f      0x4ea objs/gps_serial_test.o
 .debug_line    0x000103f9      0x16f /usr/lib/gcc/arm-none-eabi/13.2.1/thumb/v7e-m+fp/hard/libgcc.a(_arm_addsubdf3.o)
 .debug_line    0x00010568      0x184 /usr/lib/gcc/arm-none-eabi/13.2.1/thumb/v7e-m+fp/hard/libgcc.a(_arm_muldivdf3.o)
 .debug_line    0x000106ec       0x76 /usr/lib/gcc/arm-none-eabi/13.2.1/thumb/v7e-m+fp/hard/libgcc.a(_arm_truncdfsf2.o)
 .debug_line    0x00010762       0x4e /usr/lib/gcc/arm-none-eabi/13.2.1/thumb/v7e-m+fp/hard/libgcc.a(_aeabi_uldivmod.o)
 .debug_line    0x000107b0      0x574 /usr/lib/gcc/arm-none-eabi/13.2.1/thumb/v7e-m+fp/hard/libgcc.a(_udivmoddi4.o)
 .debug_line    0x00010d24       0x4a /usr/lib/gcc/arm-none-eabi/13.2.1/thumb/v7e-m+fp/hard/libgcc.a(_dvmd_tls.o)
 .debug_line    0x00010d6e      0x1af /usr/lib/gcc/arm-none-eabi/13.2.1/../../../arm-none-eabi/lib/thumb/v7e-m+fp/hard/libm.a(libm_a-sf_trunc.o)

.debug_line_str
                0x00000000      0x10c
 .debug_line_str
                0x00000000      0x10c objs/startup_stm32l431xx.o
                                 0x52 (size before relaxing)
 .debug_line_str
                0x0000010c       0x2f objs/HAL_CM4.o
 .debug_line_str
                0x0000010c       0x9c /usr/lib/gcc/arm-none-eabi/13.2.1/thumb/v7e-m+fp/hard/libgcc.a(_arm_addsubdf3.o)
 .debug_line_str
                0x0000010c       0x9c /usr/lib/gcc/arm-none-eabi/13.2.1/thumb/v7e-m+fp/hard/libgcc.a(_arm_muldivdf3.o)
 .debug_line_str
                0x0000010c       0x9c /usr/lib/gcc/arm-none-eabi/13.2.1/thumb/v7e-m+fp/hard/libgcc.a(_arm_truncdfsf2.o)
 .debug_line_str
                0x0000010c       0x97 /usr/lib/gcc/arm-none-eabi/13.2.1/thumb/v7e-m+fp/hard/libgcc.a(_aeabi_uldivmod.o)
 .debug_line_str
                0x0000010c       0x9b /usr/lib/gcc/arm-none-eabi/13.2.1/thumb/v7e-m+fp/hard/libgcc.a(_dvmd_tls.o)

.debug_info     0x00000000    0x24344
 .debug_info    0x00000000       0x30 objs/startup_stm32l431xx.o
 .debug_info    0x00000030       0x94 objs/HAL_CM4.o
 .debug_info    0x000000c4      0x324 objs/HAL_CM.o
 .debug_info    0x000003e8      0x470 objs/RTX_Conf_CM.o
 .debug_info    0x00000858     0x40d9 objs/rt_CMSIS.o
 .debug_info    0x00004931      0x4f4 objs/rt_Event.o
 .debug_info    0x00004e25      0x6b3 objs/rt_List.o
 .debug_info    0x000054d8      0x72d objs/rt_Mailbox.o
 .debug_info    0x00005c05      0x2a3 objs/rt_MemBox.o
 .debug_info    0x00005ea8      0x1b5 objs/rt_Memory.o
 .debug_info    0x0000605d      0x48b objs/rt_Mutex.o
 .debug_info    0x000064e8      0x345 objs/rt_Robin.o
 .debug_info    0x0000682d      0x507 objs/rt_Semaphore.o
 .debug_info    0x00006d34      0x7d4 objs/rt_System.o
 .debug_info    0x00007508      0x888 objs/rt_Task.o
 .debug_info    0x00007d90      0x2eb objs/rt_Time.o
 .debug_info    0x0000807b      0x5b8 objs/system_stm32l4xx.o
 .debug_info    0x00008633      0x6b6 objs/start.o
 .debug_info    0x00008ce9      0xf8a objs/board_fault.o
 .debug_info    0x00009c73      0x20f objs/bits.o
 .debug_info    0x00009e82      0xef5 objs/printf.o
 .debug_info    0x0000ad77      0xaf4 objs/libc.o
 .debug_info    0x0000b86b      0xf96 objs/rtc_api.o
 .debug_info    0x0000c801      0x123 objs/os_idle.o
 .debug_info    0x0000c924      0x7df objs/datetime.o
 .debug_info    0x0000d103      0x66e objs/console.o
 .debug_info    0x0000d771     0x1a46 objs/event.o
 .debug_info    0x0000f1b7      0xe30 objs/system_file.o
 .debug_info    0x0000ffe7      0x6c1 objs/gpio.o
 .debug_info    0x000106a8      0xa1e objs/gpio_api.o
 .debug_info    0x000110c6      0x65f objs/PeripheralPins.o
 .debug_info    0x00011725      0xafc objs/pinmap.o
 .debug_info    0x00012221      0x68a objs/mbed_pinmap_common.o
 .debug_info    0x000128ab     0x1d3c objs/serial_api.o
 .debug_info    0x000145e7     0x1c4b objs/serial_device.o
 .debug_info    0x00016232      0x453 objs/port_stubs.o
 .debug_info    0x00016685     0x1184 objs/serial_api_stubs.o
 .debug_info    0x00017809      0x447 objs/serial_wire_debug.o
 .debug_info    0x00017c50      0xa7c objs/stm32l4xx_hal.o
 .debug_info    0x000186cc      0xcb7 objs/stm32l4xx_hal_cortex.o
 .debug_info    0x00019383      0x909 objs/stm32l4xx_hal_pwr.o
 .debug_info    0x00019c8c      0xd5a objs/stm32l4xx_hal_rtc.o
 .debug_info    0x0001a9e6      0xfb6 objs/stm32l4xx_hal_rtc_ex.o
 .debug_info    0x0001b99c      0xb6d objs/stm32l4xx_hal_rcc.o
 .debug_info    0x0001c509      0xedc objs/stm32l4xx_hal_rcc_ex.o
 .debug_info    0x0001d3e5     0x180e objs/stm32l4xx_hal_uart.o
 .debug_info    0x0001ebf3      0xa98 objs/stm32l4xx_hal_uart_ex.o
 .debug_info    0x0001f68b     0x173a objs/gps_quectel.o
 .debug_info    0x00020dc5      0x55b objs/nmea.o
 .debug_info    0x00021320     0x1607 objs/settings.o
 .debug_info    0x00022927      0xfe2 objs/gps_serial_test.o
 .debug_info    0x00023909       0xd3 /usr/lib/gcc/arm-none-eabi/13.2.1/thumb/v7e-m+fp/hard/libgcc.a(_arm_addsubdf3.o)
 .debug_info    0x000239dc       0x57 /usr/lib/gcc/arm-none-eabi/13.2.1/thumb/v7e-m+fp/hard/libgcc.a(_arm_muldivdf3.o)
 .debug_info    0x00023a33       0x3f /usr/lib/gcc/arm-none-eabi/13.2.1/thumb/v7e-m+fp/hard/libgcc.a(_arm_truncdfsf2.o)
 .debug_info    0x00023a72       0x24 /usr/lib/gcc/arm-none-eabi/13.2.1/thumb/v7e-m+fp/hard/libgcc.a(_aeabi_uldivmod.o)
 .debug_info    0x00023a96      0x6f5 /usr/lib/gcc/arm-none-eabi/13.2.1/thumb/v7e-m+fp/hard/libgcc.a(_udivmoddi4.o)
 .debug_info    0x0002418b       0x3c /usr/lib/gcc/arm-none-eabi/13.2.1/thumb/v7e-m+fp/hard/libgcc.a(_dvmd_tls.o)
 .debug_info    0x000241c7      0x17d /usr/lib/gcc/arm-none-eabi/13.2.1/../../../arm-none-eabi/lib/thumb/v7e-m+fp/hard/libm.a(libm_a-sf_trunc.o)

.debug_abbrev   0x00000000     0x7200
 .debug_abbrev  0x00000000       0x24 objs/startup_stm32l431xx.o
 .debug_abbrev  0x00000024       0x28 objs/HAL_CM4.o
 .debug_abbrev  0x0000004c      0x14b objs/HAL_CM.o
 .debug_abbrev  0x00000197      0x164 objs/RTX_Conf_CM.o
 .debug_abbrev  0x000002fb      0x46f objs/rt_CMSIS.o
 .debug_abbrev  0x0000076a      0x1af objs/rt_Event.o
 .debug_abbrev  0x00000919      0x286 objs/rt_List.o
 .debug_abbrev  0x00000b9f      0x254 objs/rt_Mailbox.o
 .debug_abbrev  0x00000df3      0x196 objs/rt_MemBox.o
 .debug_abbrev  0x00000f89      0x10e objs/rt_Memory.o
 .debug_abbrev  0x00001097      0x162 objs/rt_Mutex.o
 .debug_abbrev  0x000011f9      0x13a objs/rt_Robin.o
 .debug_abbrev  0x00001333      0x183 objs/rt_Semaphore.o
 .debug_abbrev  0x000014b6      0x315 objs/rt_System.o
 .debug_abbrev  0x000017cb      0x356 objs/rt_Task.o
 .debug_abbrev  0x00001b21      0x14e objs/rt_Time.o
 .debug_abbrev  0x00001c6f      0x126 objs/system_stm32l4xx.o
 .debug_abbrev  0x00001d95      0x16c objs/start.o
 .debug_abbrev  0x00001f01      0x30c objs/board_fault.o
 .debug_abbrev  0x0000220d       0xac objs/bits.o
 .debug_abbrev  0x000022b9      0x36c objs/printf.o
 .debug_abbrev  0x00002625      0x27e objs/libc.o
 .debug_abbrev  0x000028a3      0x2a6 objs/rtc_api.o
 .debug_abbrev  0x00002b49       0x9b objs/os_idle.o
 .debug_abbrev  0x00002be4      0x1d4 objs/datetime.o
 .debug_abbrev  0x00002db8      0x218 objs/console.o
 .debug_abbrev  0x00002fd0      0x3ec objs/event.o
 .debug_abbrev  0x000033bc      0x2b6 objs/system_file.o
 .debug_abbrev  0x00003672      0x1aa objs/gpio.o
 .debug_abbrev  0x0000381c      0x283 objs/gpio_api.o
 .debug_abbrev  0x00003a9f      0x117 objs/PeripheralPins.o
 .debug_abbrev  0x00003bb6      0x2b1 objs/pinmap.o
 .debug_abbrev  0x00003e67      0x18e objs/mbed_pinmap_common.o
 .debug_abbrev  0x00003ff5      0x3c7 objs/serial_api.o
 .debug_abbrev  0x000043bc      0x3ec objs/serial_device.o
 .debug_abbrev  0x000047a8      0x1a7 objs/port_stubs.o
 .debug_abbrev  0x0000494f      0x306 objs/serial_api_stubs.o
 .debug_abbrev  0x00004c55      0x1f1 objs/serial_wire_debug.o
 .debug_abbrev  0x00004e46      0x240 objs/stm32l4xx_hal.o
 .debug_abbrev  0x00005086      0x325 objs/stm32l4xx_hal_cortex.o
 .debug_abbrev  0x000053ab      0x1d2 objs/stm32l4xx_hal_pwr.o
 .debug_abbrev  0x0000557d      0x1ff objs/stm32l4xx_hal_rtc.o
 .debug_abbrev  0x0000577c      0x20f objs/stm32l4xx_hal_rtc_ex.o
 .debug_abbrev  0x0000598b      0x2c7 objs/stm32l4xx_hal_rcc.o
 .debug_abbrev  0x00005c52      0x29f objs/stm32l4xx_hal_rcc_ex.o
 .debug_abbrev  0x00005ef1      0x255 objs/stm32l4xx_hal_uart.o
 .debug_abbrev  0x00006146      0x219 objs/stm32l4xx_hal_uart_ex.o
 .debug_abbrev  0x0000635f      0x3a1 objs/gps_quectel.o
 .debug_abbrev  0x00006700      0x1b2 objs/nmea.o
 .debug_abbrev  0x000068b2      0x382 objs/settings.o
 .debug_abbrev  0x00006c34      0x2c8 objs/gps_serial_test.o
 .debug_abbrev  0x00006efc       0x28 /usr/lib/gcc/arm-none-eabi/13.2.1/thumb/v7e-m+fp/hard/libgcc.a(_arm_addsubdf3.o)
 .debug_abbrev  0x00006f24       0x28 /usr/lib/gcc/arm-none-eabi/13.2.1/thumb/v7e-m+fp/hard/libgcc.a(_arm_muldivdf3.o)
 .debug_abbrev  0x00006f4c       0x28 /usr/lib/gcc/arm-none-eabi/13.2.1/thumb/v7e-m+fp/hard/libgcc.a(_arm_truncdfsf2.o)
 .debug_abbrev  0x00006f74       0x14 /usr/lib/gcc/arm-none-eabi/13.2.1/thumb/v7e-m+fp/hard/libgcc.a(_aeabi_uldivmod.o)
 .debug_abbrev  0x00006f88      0x16a /usr/lib/gcc/arm-none-eabi/13.2.1/thumb/v7e-m+fp/hard/libgcc.a(_udivmoddi4.o)
 .debug_abbrev  0x000070f2       0x26 /usr/lib/gcc/arm-none-eabi/13.2.1/thumb/v7e-m+fp/hard/libgcc.a(_dvmd_tls.o)
 .debug_abbrev  0x00007118       0xe8 /usr/lib/gcc/arm-none-eabi/13.2.1/../../../arm-none-eabi/lib/thumb/v7e-m+fp/hard/libm.a(libm_a-sf_trunc.o)

.debug_aranges  0x00000000     0x1b48
 .debug_aranges
                0x00000000       0x28 objs/startup_stm32l431xx.o
 .debug_aranges
                0x00000028       0x20 objs/HAL_CM4.o
 .debug_aranges
                0x00000048       0x38 objs/HAL_CM.o
 .debug_aranges
                0x00000080       0x20 objs/RTX_Conf_CM.o
 .debug_aranges
                0x000000a0      0x310 objs/rt_CMSIS.o
 .debug_aranges
                0x000003b0       0x48 objs/rt_Event.o
 .debug_aranges
                0x000003f8       0x70 objs/rt_List.o
 .debug_aranges
                0x00000468       0x50 objs/rt_Mailbox.o
 .debug_aranges
                0x000004b8       0x38 objs/rt_MemBox.o
 .debug_aranges
                0x000004f0       0x30 objs/rt_Memory.o
 .debug_aranges
                0x00000520       0x38 objs/rt_Mutex.o
 .debug_aranges
                0x00000558       0x28 objs/rt_Robin.o
 .debug_aranges
                0x00000580       0x48 objs/rt_Semaphore.o
 .debug_aranges
                0x000005c8       0x90 objs/rt_System.o
 .debug_aranges
                0x00000658       0x80 objs/rt_Task.o
 .debug_aranges
                0x000006d8       0x38 objs/rt_Time.o
 .debug_aranges
                0x00000710       0x28 objs/system_stm32l4xx.o
 .debug_aranges
                0x00000738       0x20 objs/start.o
 .debug_aranges
                0x00000758       0x58 objs/board_fault.o
 .debug_aranges
                0x000007b0       0x58 objs/bits.o
 .debug_aranges
                0x00000808       0x88 objs/printf.o
 .debug_aranges
                0x00000890       0x80 objs/libc.o
 .debug_aranges
                0x00000910       0x48 objs/rtc_api.o
 .debug_aranges
                0x00000958       0x20 objs/os_idle.o
 .debug_aranges
                0x00000978       0x48 objs/datetime.o
 .debug_aranges
                0x000009c0       0x48 objs/console.o
 .debug_aranges
                0x00000a08       0x70 objs/event.o
 .debug_aranges
                0x00000a78       0x58 objs/system_file.o
 .debug_aranges
                0x00000ad0       0x58 objs/gpio.o
 .debug_aranges
                0x00000b28       0x48 objs/gpio_api.o
 .debug_aranges
                0x00000b70       0x18 objs/PeripheralPins.o
 .debug_aranges
                0x00000b88       0x78 objs/pinmap.o
 .debug_aranges
                0x00000c00       0x60 objs/mbed_pinmap_common.o
 .debug_aranges
                0x00000c60       0xf8 objs/serial_api.o
 .debug_aranges
                0x00000d58      0x108 objs/serial_device.o
 .debug_aranges
                0x00000e60       0x90 objs/port_stubs.o
 .debug_aranges
                0x00000ef0       0xb0 objs/serial_api_stubs.o
 .debug_aranges
                0x00000fa0       0x40 objs/serial_wire_debug.o
 .debug_aranges
                0x00000fe0      0x130 objs/stm32l4xx_hal.o
 .debug_aranges
                0x00001110      0x118 objs/stm32l4xx_hal_cortex.o
 .debug_aranges
                0x00001228       0x98 objs/stm32l4xx_hal_pwr.o
 .debug_aranges
                0x000012c0       0xe8 objs/stm32l4xx_hal_rtc.o
 .debug_aranges
                0x000013a8      0x140 objs/stm32l4xx_hal_rtc_ex.o
 .debug_aranges
                0x000014e8       0x88 objs/stm32l4xx_hal_rcc.o
 .debug_aranges
                0x00001570       0xf0 objs/stm32l4xx_hal_rcc_ex.o
 .debug_aranges
                0x00001660      0x228 objs/stm32l4xx_hal_uart.o
 .debug_aranges
                0x00001888       0x78 objs/stm32l4xx_hal_uart_ex.o
 .debug_aranges
                0x00001900       0x78 objs/gps_quectel.o
 .debug_aranges
                0x00001978       0x38 objs/nmea.o
 .debug_aranges
                0x000019b0       0x68 objs/settings.o
 .debug_aranges
                0x00001a18       0x50 objs/gps_serial_test.o
 .debug_aranges
                0x00001a68       0x20 /usr/lib/gcc/arm-none-eabi/13.2.1/thumb/v7e-m+fp/hard/libgcc.a(_arm_addsubdf3.o)
 .debug_aranges
                0x00001a88       0x20 /usr/lib/gcc/arm-none-eabi/13.2.1/thumb/v7e-m+fp/hard/libgcc.a(_arm_muldivdf3.o)
 .debug_aranges
                0x00001aa8       0x20 /usr/lib/gcc/arm-none-eabi/13.2.1/thumb/v7e-m+fp/hard/libgcc.a(_arm_truncdfsf2.o)
 .debug_aranges
                0x00001ac8       0x20 /usr/lib/gcc/arm-none-eabi/13.2.1/thumb/v7e-m+fp/hard/libgcc.a(_aeabi_uldivmod.o)
 .debug_aranges
                0x00001ae8       0x20 /usr/lib/gcc/arm-none-eabi/13.2.1/thumb/v7e-m+fp/hard/libgcc.a(_udivmoddi4.o)
 .debug_aranges
                0x00001b08       0x20 /usr/lib/gcc/arm-none-eabi/13.2.1/thumb/v7e-m+fp/hard/libgcc.a(_dvmd_tls.o)
 .debug_aranges
                0x00001b28       0x20 /usr/lib/gcc/arm-none-eabi/13.2.1/../../../arm-none-eabi/lib/thumb/v7e-m+fp/hard/libm.a(libm_a-sf_trunc.o)

.debug_str      0x00000000     0x96f0
 .debug_str     0x00000000     0x96f0 objs/startup_stm32l431xx.o
                                 0x6c (size before relaxing)
 .debug_str     0x000096f0       0xac objs/HAL_CM4.o
 .debug_str     0x000096f0      0x299 objs/HAL_CM.o
 .debug_str     0x000096f0      0x4f3 objs/RTX_Conf_CM.o
 .debug_str     0x000096f0     0x1358 objs/rt_CMSIS.o
 .debug_str     0x000096f0      0x385 objs/rt_Event.o
 .debug_str     0x000096f0      0x3b2 objs/rt_List.o
 .debug_str     0x000096f0      0x3d5 objs/rt_Mailbox.o
 .debug_str     0x000096f0      0x21f objs/rt_MemBox.o
 .debug_str     0x000096f0      0x1e4 objs/rt_Memory.o
 .debug_str     0x000096f0      0x323 objs/rt_Mutex.o
 .debug_str     0x000096f0      0x2c9 objs/rt_Robin.o
 .debug_str     0x000096f0      0x36a objs/rt_Semaphore.o
 .debug_str     0x000096f0      0x4e1 objs/rt_System.o
 .debug_str     0x000096f0      0x569 objs/rt_Task.o
 .debug_str     0x000096f0      0x2a6 objs/rt_Time.o
 .debug_str     0x000096f0      0x3e8 objs/system_stm32l4xx.o
 .debug_str     0x000096f0      0x98d objs/start.o
 .debug_str     0x000096f0     0x1666 objs/board_fault.o
 .debug_str     0x000096f0      0x22c objs/bits.o
 .debug_str     0x000096f0      0xbd3 objs/printf.o
 .debug_str     0x000096f0      0x92c objs/libc.o
 .debug_str     0x000096f0      0xec4 objs/rtc_api.o
 .debug_str     0x000096f0      0x221 objs/os_idle.o
 .debug_str     0x000096f0      0x879 objs/datetime.o
 .debug_str     0x000096f0      0x62b objs/console.o
 .debug_str     0x000096f0     0x1c0e objs/event.o
 .debug_str     0x000096f0      0xdbd objs/system_file.o
 .debug_str     0x000096f0      0x5e7 objs/gpio.o
 .debug_str     0x000096f0      0x7f1 objs/gpio_api.o
 .debug_str     0x000096f0      0x603 objs/PeripheralPins.o
 .debug_str     0x000096f0      0x695 objs/pinmap.o
 .debug_str     0x000096f0      0x5b2 objs/mbed_pinmap_common.o
 .debug_str     0x000096f0     0x16e8 objs/serial_api.o
 .debug_str     0x000096f0     0x159f objs/serial_device.o
 .debug_str     0x000096f0      0x47c objs/port_stubs.o
 .debug_str     0x000096f0      0xcf4 objs/serial_api_stubs.o
 .debug_str     0x000096f0      0x30b objs/serial_wire_debug.o
 .debug_str     0x000096f0      0xc0d objs/stm32l4xx_hal.o
 .debug_str     0x000096f0      0xb1e objs/stm32l4xx_hal_cortex.o
 .debug_str     0x000096f0      0x67b objs/stm32l4xx_hal_pwr.o
 .debug_str     0x000096f0      0x84f objs/stm32l4xx_hal_rtc.o
 .debug_str     0x000096f0      0xb6a objs/stm32l4xx_hal_rtc_ex.o
 .debug_str     0x000096f0      0x81c objs/stm32l4xx_hal_rcc.o
 .debug_str     0x000096f0      0xb74 objs/stm32l4xx_hal_rcc_ex.o
 .debug_str     0x000096f0      0xf8f objs/stm32l4xx_hal_uart.o
 .debug_str     0x000096f0      0x8d7 objs/stm32l4xx_hal_uart_ex.o
 .debug_str     0x000096f0     0x1c53 objs/gps_quectel.o
 .debug_str     0x000096f0      0x416 objs/nmea.o
 .debug_str     0x000096f0     0x1a66 objs/settings.o
 .debug_str     0x000096f0      0xdea objs/gps_serial_test.o
 .debug_str     0x000096f0      0x154 /usr/lib/gcc/arm-none-eabi/13.2.1/thumb/v7e-m+fp/hard/libgcc.a(_arm_addsubdf3.o)
 .debug_str     0x000096f0       0xd4 /usr/lib/gcc/arm-none-eabi/13.2.1/thumb/v7e-m+fp/hard/libgcc.a(_arm_muldivdf3.o)
 .debug_str     0x000096f0       0xc1 /usr/lib/gcc/arm-none-eabi/13.2.1/thumb/v7e-m+fp/hard/libgcc.a(_arm_truncdfsf2.o)
 .debug_str     0x000096f0       0xa3 /usr/lib/gcc/arm-none-eabi/13.2.1/thumb/v7e-m+fp/hard/libgcc.a(_aeabi_uldivmod.o)
 .debug_str     0x000096f0      0x685 /usr/lib/gcc/arm-none-eabi/13.2.1/thumb/v7e-m+fp/hard/libgcc.a(_udivmoddi4.o)
 .debug_str     0x000096f0       0xc3 /usr/lib/gcc/arm-none-eabi/13.2.1/thumb/v7e-m+fp/hard/libgcc.a(_dvmd_tls.o)
 .debug_str     0x000096f0      0x1fb /usr/lib/gcc/arm-none-eabi/13.2.1/../../../arm-none-eabi/lib/thumb/v7e-m+fp/hard/libm.a(libm_a-sf_trunc.o)

.debug_rnglists
                0x00000000     0x142c
 .debug_rnglists
                0x00000000       0x19 objs/startup_stm32l431xx.o
 .debug_rnglists
                0x00000019       0x26 objs/HAL_CM.o
 .debug_rnglists
                0x0000003f       0x13 objs/RTX_Conf_CM.o
 .debug_rnglists
                0x00000052      0x255 objs/rt_CMSIS.o
 .debug_rnglists
                0x000002a7       0x34 objs/rt_Event.o
 .debug_rnglists
                0x000002db       0x52 objs/rt_List.o
 .debug_rnglists
                0x0000032d       0x3b objs/rt_Mailbox.o
 .debug_rnglists
                0x00000368       0x26 objs/rt_MemBox.o
 .debug_rnglists
                0x0000038e       0x20 objs/rt_Memory.o
 .debug_rnglists
                0x000003ae       0x28 objs/rt_Mutex.o
 .debug_rnglists
                0x000003d6       0x19 objs/rt_Robin.o
 .debug_rnglists
                0x000003ef       0x32 objs/rt_Semaphore.o
 .debug_rnglists
                0x00000421       0x6b objs/rt_System.o
 .debug_rnglists
                0x0000048c       0x61 objs/rt_Task.o
 .debug_rnglists
                0x000004ed       0x25 objs/rt_Time.o
 .debug_rnglists
                0x00000512       0x1a objs/system_stm32l4xx.o
 .debug_rnglists
                0x0000052c       0x13 objs/start.o
 .debug_rnglists
                0x0000053f       0x3f objs/board_fault.o
 .debug_rnglists
                0x0000057e       0x3f objs/bits.o
 .debug_rnglists
                0x000005bd       0x63 objs/printf.o
 .debug_rnglists
                0x00000620       0x5e objs/libc.o
 .debug_rnglists
                0x0000067e       0x34 objs/rtc_api.o
 .debug_rnglists
                0x000006b2       0x13 objs/os_idle.o
 .debug_rnglists
                0x000006c5       0x33 objs/datetime.o
 .debug_rnglists
                0x000006f8       0x31 objs/console.o
 .debug_rnglists
                0x00000729       0x63 objs/event.o
 .debug_rnglists
                0x0000078c       0x3e objs/system_file.o
 .debug_rnglists
                0x000007ca       0x3d objs/gpio.o
 .debug_rnglists
                0x00000807       0x33 objs/gpio_api.o
 .debug_rnglists
                0x0000083a       0x57 objs/pinmap.o
 .debug_rnglists
                0x00000891       0x44 objs/mbed_pinmap_common.o
 .debug_rnglists
                0x000008d5       0xbd objs/serial_api.o
 .debug_rnglists
                0x00000992       0xc8 objs/serial_device.o
 .debug_rnglists
                0x00000a5a       0x67 objs/port_stubs.o
 .debug_rnglists
                0x00000ac1       0x80 objs/serial_api_stubs.o
 .debug_rnglists
                0x00000b41       0x2b objs/serial_wire_debug.o
 .debug_rnglists
                0x00000b6c       0xdf objs/stm32l4xx_hal.o
 .debug_rnglists
                0x00000c4b       0xce objs/stm32l4xx_hal_cortex.o
 .debug_rnglists
                0x00000d19       0x6e objs/stm32l4xx_hal_pwr.o
 .debug_rnglists
                0x00000d87       0xb4 objs/stm32l4xx_hal_rtc.o
 .debug_rnglists
                0x00000e3b       0xfc objs/stm32l4xx_hal_rtc_ex.o
 .debug_rnglists
                0x00000f37       0x67 objs/stm32l4xx_hal_rcc.o
 .debug_rnglists
                0x00000f9e       0xba objs/stm32l4xx_hal_rcc_ex.o
 .debug_rnglists
                0x00001058      0x1d3 objs/stm32l4xx_hal_uart.o
 .debug_rnglists
                0x0000122b       0x5a objs/stm32l4xx_hal_uart_ex.o
 .debug_rnglists
                0x00001285       0x63 objs/gps_quectel.o
 .debug_rnglists
                0x000012e8       0x27 objs/nmea.o
 .debug_rnglists
                0x0000130f       0x4d objs/settings.o
 .debug_rnglists
                0x0000135c       0x39 objs/gps_serial_test.o
 .debug_rnglists
                0x00001395       0x84 /usr/lib/gcc/arm-none-eabi/13.2.1/thumb/v7e-m+fp/hard/libgcc.a(_udivmoddi4.o)
 .debug_rnglists
                0x00001419       0x13 /usr/lib/gcc/arm-none-eabi/13.2.1/../../../arm-none-eabi/lib/thumb/v7e-m+fp/hard/libm.a(libm_a-sf_trunc.o)

.debug_frame    0x00000000     0x67c4
 .debug_frame   0x00000000       0xa8 objs/HAL_CM.o
 .debug_frame   0x000000a8       0x20 objs/RTX_Conf_CM.o
 .debug_frame   0x000000c8      0xdc0 objs/rt_CMSIS.o
 .debug_frame   0x00000e88       0xe8 objs/rt_Event.o
 .debug_frame   0x00000f70      0x1bc objs/rt_List.o
 .debug_frame   0x0000112c      0x114 objs/rt_Mailbox.o
 .debug_frame   0x00001240       0xac objs/rt_MemBox.o
 .debug_frame   0x000012ec       0x88 objs/rt_Memory.o
 .debug_frame   0x00001374       0xa4 objs/rt_Mutex.o
 .debug_frame   0x00001418       0x54 objs/rt_Robin.o
 .debug_frame   0x0000146c       0xec objs/rt_Semaphore.o
 .debug_frame   0x00001558      0x1f0 objs/rt_System.o
 .debug_frame   0x00001748      0x1e8 objs/rt_Task.o
 .debug_frame   0x00001930       0xa0 objs/rt_Time.o
 .debug_frame   0x000019d0       0x58 objs/system_stm32l4xx.o
 .debug_frame   0x00001a28       0x2c objs/start.o
 .debug_frame   0x00001a54       0xfc objs/board_fault.o
 .debug_frame   0x00001b50      0x164 objs/bits.o
 .debug_frame   0x00001cb4      0x268 objs/printf.o
 .debug_frame   0x00001f1c      0x204 objs/libc.o
 .debug_frame   0x00002120       0xe8 objs/rtc_api.o
 .debug_frame   0x00002208       0x28 objs/os_idle.o
 .debug_frame   0x00002230       0xf0 objs/datetime.o
 .debug_frame   0x00002320       0xdc objs/console.o
 .debug_frame   0x000023fc      0x19c objs/event.o
 .debug_frame   0x00002598      0x124 objs/system_file.o
 .debug_frame   0x000026bc      0x134 objs/gpio.o
 .debug_frame   0x000027f0       0xec objs/gpio_api.o
 .debug_frame   0x000028dc      0x1e0 objs/pinmap.o
 .debug_frame   0x00002abc      0x168 objs/mbed_pinmap_common.o
 .debug_frame   0x00002c24      0x434 objs/serial_api.o
 .debug_frame   0x00003058      0x460 objs/serial_device.o
 .debug_frame   0x000034b8      0x22c objs/port_stubs.o
 .debug_frame   0x000036e4      0x2e8 objs/serial_api_stubs.o
 .debug_frame   0x000039cc       0xdc objs/serial_wire_debug.o
 .debug_frame   0x00003aa8      0x498 objs/stm32l4xx_hal.o
 .debug_frame   0x00003f40      0x498 objs/stm32l4xx_hal_cortex.o
 .debug_frame   0x000043d8      0x230 objs/stm32l4xx_hal_pwr.o
 .debug_frame   0x00004608      0x3f4 objs/stm32l4xx_hal_rtc.o
 .debug_frame   0x000049fc      0x598 objs/stm32l4xx_hal_rtc_ex.o
 .debug_frame   0x00004f94      0x1f4 objs/stm32l4xx_hal_rcc.o
 .debug_frame   0x00005188      0x3d0 objs/stm32l4xx_hal_rcc_ex.o
 .debug_frame   0x00005558      0x9ec objs/stm32l4xx_hal_uart.o
 .debug_frame   0x00005f44      0x1e0 objs/stm32l4xx_hal_uart_ex.o
 .debug_frame   0x00006124      0x1a4 objs/gps_quectel.o
 .debug_frame   0x000062c8       0xb8 objs/nmea.o
 .debug_frame   0x00006380      0x170 objs/settings.o
 .debug_frame   0x000064f0      0x134 objs/gps_serial_test.o
 .debug_frame   0x00006624       0xac /usr/lib/gcc/arm-none-eabi/13.2.1/thumb/v7e-m+fp/hard/libgcc.a(_arm_addsubdf3.o)
 .debug_frame   0x000066d0       0x50 /usr/lib/gcc/arm-none-eabi/13.2.1/thumb/v7e-m+fp/hard/libgcc.a(_arm_muldivdf3.o)
 .debug_frame   0x00006720       0x24 /usr/lib/gcc/arm-none-eabi/13.2.1/thumb/v7e-m+fp/hard/libgcc.a(_arm_truncdfsf2.o)
 .debug_frame   0x00006744       0x2c /usr/lib/gcc/arm-none-eabi/13.2.1/thumb/v7e-m+fp/hard/libgcc.a(_aeabi_uldivmod.o)
 .debug_frame   0x00006770       0x34 /usr/lib/gcc/arm-none-eabi/13.2.1/thumb/v7e-m+fp/hard/libgcc.a(_udivmoddi4.o)
 .debug_frame   0x000067a4       0x20 /usr/lib/gcc/arm-none-eabi/13.2.1/../../../arm-none-eabi/lib/thumb/v7e-m+fp/hard/libm.a(libm_a-sf_trunc.o)

.debug_loclists
                0x00000000      0xb3c
 .debug_loclists
                0x00000000      0x9e4 /usr/lib/gcc/arm-none-eabi/13.2.1/thumb/v7e-m+fp/hard/libgcc.a(_udivmoddi4.o)
 .debug_loclists
                0x000009e4      0x158 /usr/lib/gcc/arm-none-eabi/13.2.1/../../../arm-none-eabi/lib/thumb/v7e-m+fp/hard/libm.a(libm_a-sf_trunc.o)

Cross Reference Table

Symbol                                            File
ADC1_IRQHandler                                   objs/startup_stm32l431xx.o
ADC_ConversionStop                                objs/stm32l4xx_hal_adc.o
                                                  objs/stm32l4xx_hal_adc_ex.o
ADC_DMAConvCplt                                   objs/stm32l4xx_hal_adc.o
ADC_DMAError                                      objs/stm32l4xx_hal_adc.o
ADC_DMAHalfConvCplt                               objs/stm32l4xx_hal_adc.o
ADC_Disable                                       objs/stm32l4xx_hal_adc.o
                                                  objs/stm32l4xx_hal_adc_ex.o
ADC_Enable                                        objs/stm32l4xx_hal_adc.o
                                                  objs/stm32l4xx_hal_adc_ex.o
AHBPrescTable                                     objs/system_stm32l4xx.o
                                                  objs/stm32l4xx_hal_rcc.o
APBPrescTable                                     objs/system_stm32l4xx.o
                                                  objs/stm32l4xx_hal_rcc.o
BusFault_Handler                                  objs/startup_stm32l431xx.o
CAN1_RX0_IRQHandler                               objs/startup_stm32l431xx.o
CAN1_RX1_IRQHandler                               objs/startup_stm32l431xx.o
CAN1_SCE_IRQHandler                               objs/startup_stm32l431xx.o
CAN1_TX_IRQHandler                                objs/startup_stm32l431xx.o
CMSIS_RTOS_API_Version                            objs/RTX_Conf_CM.o
CMSIS_RTOS_RTX_Version                            objs/RTX_Conf_CM.o
COMP_IRQHandler                                   objs/startup_stm32l431xx.o
CRS_IRQHandler                                    objs/startup_stm32l431xx.o
DMA1_Channel1_IRQHandler                          objs/startup_stm32l431xx.o
DMA1_Channel2_IRQHandler                          objs/startup_stm32l431xx.o
DMA1_Channel3_IRQHandler                          objs/startup_stm32l431xx.o
DMA1_Channel4_IRQHandler                          objs/startup_stm32l431xx.o
DMA1_Channel5_IRQHandler                          objs/startup_stm32l431xx.o
DMA1_Channel6_IRQHandler                          objs/startup_stm32l431xx.o
DMA1_Channel7_IRQHandler                          objs/startup_stm32l431xx.o
DMA2_Channel1_IRQHandler                          objs/startup_stm32l431xx.o
DMA2_Channel2_IRQHandler                          objs/startup_stm32l431xx.o
DMA2_Channel3_IRQHandler                          objs/startup_stm32l431xx.o
DMA2_Channel4_IRQHandler                          objs/startup_stm32l431xx.o
DMA2_Channel5_IRQHandler                          objs/startup_stm32l431xx.o
DMA2_Channel6_IRQHandler                          objs/startup_stm32l431xx.o
DMA2_Channel7_IRQHandler                          objs/startup_stm32l431xx.o
DebugMon_Handler                                  objs/startup_stm32l431xx.o
Default_Handler                                   objs/startup_stm32l431xx.o
EXTI0_IRQHandler                                  objs/startup_stm32l431xx.o
EXTI15_10_IRQHandler                              objs/startup_stm32l431xx.o
EXTI1_IRQHandler                                  objs/startup_stm32l431xx.o
EXTI2_IRQHandler                                  objs/startup_stm32l431xx.o
EXTI3_IRQHandler                                  objs/startup_stm32l431xx.o
EXTI4_IRQHandler                                  objs/startup_stm32l431xx.o
EXTI9_5_IRQHandler                                objs/startup_stm32l431xx.o
FLASH_IRQHandler                                  objs/startup_stm32l431xx.o
FPU_IRQHandler                                    objs/startup_stm32l431xx.o
HAL_ADCEx_Calibration_GetValue                    objs/stm32l4xx_hal_adc_ex.o
                                                  objs/analogin_device.o
HAL_ADCEx_Calibration_SetValue                    objs/stm32l4xx_hal_adc_ex.o
HAL_ADCEx_Calibration_Start                       objs/stm32l4xx_hal_adc_ex.o
                                                  objs/analogin_device.o
HAL_ADCEx_DisableInjectedQueue                    objs/stm32l4xx_hal_adc_ex.o
HAL_ADCEx_DisableVoltageRegulator                 objs/stm32l4xx_hal_adc_ex.o
HAL_ADCEx_EnableInjectedQueue                     objs/stm32l4xx_hal_adc_ex.o
HAL_ADCEx_EndOfSamplingCallback                   objs/stm32l4xx_hal_adc_ex.o
                                                  objs/stm32l4xx_hal_adc.o
HAL_ADCEx_EnterADCDeepPowerDownMode               objs/stm32l4xx_hal_adc_ex.o
HAL_ADCEx_InjectedConfigChannel                   objs/stm32l4xx_hal_adc_ex.o
HAL_ADCEx_InjectedConvCpltCallback                objs/stm32l4xx_hal_adc_ex.o
                                                  objs/stm32l4xx_hal_adc.o
HAL_ADCEx_InjectedGetValue                        objs/stm32l4xx_hal_adc_ex.o
HAL_ADCEx_InjectedPollForConversion               objs/stm32l4xx_hal_adc_ex.o
HAL_ADCEx_InjectedQueueOverflowCallback           objs/stm32l4xx_hal_adc_ex.o
                                                  objs/stm32l4xx_hal_adc.o
HAL_ADCEx_InjectedStart                           objs/stm32l4xx_hal_adc_ex.o
HAL_ADCEx_InjectedStart_IT                        objs/stm32l4xx_hal_adc_ex.o
HAL_ADCEx_InjectedStop                            objs/stm32l4xx_hal_adc_ex.o
HAL_ADCEx_InjectedStop_IT                         objs/stm32l4xx_hal_adc_ex.o
HAL_ADCEx_LevelOutOfWindow2Callback               objs/stm32l4xx_hal_adc_ex.o
                                                  objs/stm32l4xx_hal_adc.o
HAL_ADCEx_LevelOutOfWindow3Callback               objs/stm32l4xx_hal_adc_ex.o
                                                  objs/stm32l4xx_hal_adc.o
HAL_ADCEx_RegularStop                             objs/stm32l4xx_hal_adc_ex.o
HAL_ADCEx_RegularStop_DMA                         objs/stm32l4xx_hal_adc_ex.o
HAL_ADCEx_RegularStop_IT                          objs/stm32l4xx_hal_adc_ex.o
HAL_ADC_AnalogWDGConfig                           objs/stm32l4xx_hal_adc.o
HAL_ADC_ConfigChannel                             objs/stm32l4xx_hal_adc.o
                                                  objs/analogin_device.o
HAL_ADC_ConvCpltCallback                          objs/stm32l4xx_hal_adc.o
HAL_ADC_ConvHalfCpltCallback                      objs/stm32l4xx_hal_adc.o
HAL_ADC_DeInit                                    objs/stm32l4xx_hal_adc.o
HAL_ADC_ErrorCallback                             objs/stm32l4xx_hal_adc.o
HAL_ADC_GetError                                  objs/stm32l4xx_hal_adc.o
HAL_ADC_GetState                                  objs/stm32l4xx_hal_adc.o
HAL_ADC_GetValue                                  objs/stm32l4xx_hal_adc.o
                                                  objs/analogin_device.o
HAL_ADC_IRQHandler                                objs/stm32l4xx_hal_adc.o
HAL_ADC_Init                                      objs/stm32l4xx_hal_adc.o
                                                  objs/analogin_device.o
HAL_ADC_LevelOutOfWindowCallback                  objs/stm32l4xx_hal_adc.o
HAL_ADC_MspDeInit                                 objs/stm32l4xx_hal_adc.o
HAL_ADC_MspInit                                   objs/stm32l4xx_hal_adc.o
HAL_ADC_PollForConversion                         objs/stm32l4xx_hal_adc.o
                                                  objs/analogin_device.o
HAL_ADC_PollForEvent                              objs/stm32l4xx_hal_adc.o
HAL_ADC_Start                                     objs/stm32l4xx_hal_adc.o
                                                  objs/analogin_device.o
HAL_ADC_Start_DMA                                 objs/stm32l4xx_hal_adc.o
HAL_ADC_Start_IT                                  objs/stm32l4xx_hal_adc.o
HAL_ADC_Stop                                      objs/stm32l4xx_hal_adc.o
HAL_ADC_Stop_DMA                                  objs/stm32l4xx_hal_adc.o
HAL_ADC_Stop_IT                                   objs/stm32l4xx_hal_adc.o
HAL_DBGMCU_DisableDBGSleepMode                    objs/stm32l4xx_hal.o
HAL_DBGMCU_DisableDBGStandbyMode                  objs/stm32l4xx_hal.o
HAL_DBGMCU_DisableDBGStopMode                     objs/stm32l4xx_hal.o
HAL_DBGMCU_EnableDBGSleepMode                     objs/stm32l4xx_hal.o
HAL_DBGMCU_EnableDBGStandbyMode                   objs/stm32l4xx_hal.o
HAL_DBGMCU_EnableDBGStopMode                      objs/stm32l4xx_hal.o
HAL_DMA_Abort                                     objs/stm32l4xx_hal_adc_ex.o
                                                  objs/stm32l4xx_hal_adc.o
                                                  objs/stm32l4xx_hal_uart.o
HAL_DMA_Abort_IT                                  objs/stm32l4xx_hal_tim.o
                                                  objs/stm32l4xx_hal_i2c.o
                                                  objs/stm32l4xx_hal_uart.o
HAL_DMA_GetError                                  objs/stm32l4xx_hal_uart.o
HAL_DMA_GetState                                  objs/stm32l4xx_hal_i2c.o
HAL_DMA_Start_IT                                  objs/stm32l4xx_hal_tim.o
                                                  objs/stm32l4xx_hal_adc.o
                                                  objs/stm32l4xx_hal_i2c.o
                                                  objs/stm32l4xx_hal_uart.o
HAL_DeInit                                        objs/stm32l4xx_hal.o
HAL_Delay                                         objs/stm32l4xx_hal.o
HAL_GPIO_Init                                     objs/stm32l4xx_hal_rcc_ex.o
                                                  objs/stm32l4xx_hal_rcc.o
HAL_GetDEVID                                      objs/stm32l4xx_hal.o
HAL_GetHalVersion                                 objs/stm32l4xx_hal.o
HAL_GetREVID                                      objs/stm32l4xx_hal.o
HAL_GetTick                                       objs/port_stubs.o
                                                  objs/stm32l4xx_hal_adc_ex.o
                                                  objs/stm32l4xx_hal_adc.o
                                                  objs/stm32l4xx_hal_i2c.o
                                                  objs/stm32l4xx_hal_uart_ex.o
                                                  objs/stm32l4xx_hal_uart.o
                                                  objs/stm32l4xx_hal_rcc_ex.o
                                                  objs/stm32l4xx_hal_rcc.o
                                                  objs/stm32l4xx_hal_rtc_ex.o
                                                  objs/stm32l4xx_hal_rtc.o
HAL_GetTickFreq                                   objs/stm32l4xx_hal.o
HAL_GetTickPrio                                   objs/stm32l4xx_hal.o
HAL_GetUIDw0                                      objs/stm32l4xx_hal.o
HAL_GetUIDw1                                      objs/stm32l4xx_hal.o
HAL_GetUIDw2                                      objs/stm32l4xx_hal.o
HAL_HalfDuplex_EnableReceiver                     objs/stm32l4xx_hal_uart.o
HAL_HalfDuplex_EnableTransmitter                  objs/stm32l4xx_hal_uart.o
HAL_HalfDuplex_Init                               objs/stm32l4xx_hal_uart.o
HAL_I2CEx_ConfigAnalogFilter                      objs/stm32l4xx_hal_i2c_ex.o
                                                  objs/i2c_api.o
HAL_I2CEx_ConfigDigitalFilter                     objs/stm32l4xx_hal_i2c_ex.o
HAL_I2CEx_DisableFastModePlus                     objs/stm32l4xx_hal_i2c_ex.o
HAL_I2CEx_DisableWakeUp                           objs/stm32l4xx_hal_i2c_ex.o
HAL_I2CEx_EnableFastModePlus                      objs/stm32l4xx_hal_i2c_ex.o
                                                  objs/i2c_api.o
HAL_I2CEx_EnableWakeUp                            objs/stm32l4xx_hal_i2c_ex.o
HAL_I2C_AbortCpltCallback                         objs/stm32l4xx_hal_i2c.o
HAL_I2C_AddrCallback                              objs/stm32l4xx_hal_i2c.o
HAL_I2C_DeInit                                    objs/stm32l4xx_hal_i2c.o
                                                  objs/i2c_api.o
HAL_I2C_DisableListen_IT                          objs/stm32l4xx_hal_i2c.o
HAL_I2C_ER_IRQHandler                             objs/stm32l4xx_hal_i2c.o
                                                  objs/i2c_api.o
HAL_I2C_EV_IRQHandler                             objs/stm32l4xx_hal_i2c.o
                                                  objs/i2c_api.o
HAL_I2C_EnableListen_IT                           objs/stm32l4xx_hal_i2c.o
HAL_I2C_ErrorCallback                             objs/i2c_api.o
HAL_I2C_GetError                                  objs/stm32l4xx_hal_i2c.o
HAL_I2C_GetMode                                   objs/stm32l4xx_hal_i2c.o
HAL_I2C_GetState                                  objs/stm32l4xx_hal_i2c.o
HAL_I2C_Init                                      objs/stm32l4xx_hal_i2c.o
                                                  objs/i2c_api.o
HAL_I2C_IsDeviceReady                             objs/stm32l4xx_hal_i2c.o
HAL_I2C_ListenCpltCallback                        objs/stm32l4xx_hal_i2c.o
HAL_I2C_MasterRxCpltCallback                      objs/i2c_api.o
HAL_I2C_MasterTxCpltCallback                      objs/i2c_api.o
HAL_I2C_Master_Abort_IT                           objs/stm32l4xx_hal_i2c.o
HAL_I2C_Master_Receive                            objs/stm32l4xx_hal_i2c.o
HAL_I2C_Master_Receive_DMA                        objs/stm32l4xx_hal_i2c.o
HAL_I2C_Master_Receive_IT                         objs/stm32l4xx_hal_i2c.o
HAL_I2C_Master_Seq_Receive_DMA                    objs/stm32l4xx_hal_i2c.o
HAL_I2C_Master_Seq_Receive_IT                     objs/stm32l4xx_hal_i2c.o
                                                  objs/i2c_api.o
HAL_I2C_Master_Seq_Transmit_DMA                   objs/stm32l4xx_hal_i2c.o
HAL_I2C_Master_Seq_Transmit_IT                    objs/stm32l4xx_hal_i2c.o
                                                  objs/i2c_api.o
HAL_I2C_Master_Transmit                           objs/stm32l4xx_hal_i2c.o
HAL_I2C_Master_Transmit_DMA                       objs/stm32l4xx_hal_i2c.o
HAL_I2C_Master_Transmit_IT                        objs/stm32l4xx_hal_i2c.o
HAL_I2C_MemRxCpltCallback                         objs/stm32l4xx_hal_i2c.o
HAL_I2C_MemTxCpltCallback                         objs/stm32l4xx_hal_i2c.o
HAL_I2C_Mem_Read                                  objs/stm32l4xx_hal_i2c.o
HAL_I2C_Mem_Read_DMA                              objs/stm32l4xx_hal_i2c.o
HAL_I2C_Mem_Read_IT                               objs/stm32l4xx_hal_i2c.o
HAL_I2C_Mem_Write                                 objs/stm32l4xx_hal_i2c.o
HAL_I2C_Mem_Write_DMA                             objs/stm32l4xx_hal_i2c.o
HAL_I2C_Mem_Write_IT                              objs/stm32l4xx_hal_i2c.o
HAL_I2C_MspDeInit                                 objs/stm32l4xx_hal_i2c.o
HAL_I2C_MspInit                                   objs/stm32l4xx_hal_i2c.o
HAL_I2C_SlaveRxCpltCallback                       objs/stm32l4xx_hal_i2c.o
HAL_I2C_SlaveTxCpltCallback                       objs/stm32l4xx_hal_i2c.o
HAL_I2C_Slave_Receive                             objs/stm32l4xx_hal_i2c.o
HAL_I2C_Slave_Receive_DMA                         objs/stm32l4xx_hal_i2c.o
HAL_I2C_Slave_Receive_IT                          objs/stm32l4xx_hal_i2c.o
HAL_I2C_Slave_Seq_Receive_DMA                     objs/stm32l4xx_hal_i2c.o
HAL_I2C_Slave_Seq_Receive_IT                      objs/stm32l4xx_hal_i2c.o
HAL_I2C_Slave_Seq_Transmit_DMA                    objs/stm32l4xx_hal_i2c.o
HAL_I2C_Slave_Seq_Transmit_IT                     objs/stm32l4xx_hal_i2c.o
HAL_I2C_Slave_Transmit                            objs/stm32l4xx_hal_i2c.o
HAL_I2C_Slave_Transmit_DMA                        objs/stm32l4xx_hal_i2c.o
HAL_I2C_Slave_Transmit_IT                         objs/stm32l4xx_hal_i2c.o
HAL_IncTick                                       objs/stm32l4xx_hal.o
                                                  objs/rt_System.o
HAL_Init                                          objs/stm32l4xx_hal.o
                                                  objs/start.o
HAL_InitTick                                      objs/stm32l4xx_hal.o
                                                  objs/stm32l4xx_hal_rcc.o
HAL_LIN_Init                                      objs/stm32l4xx_hal_uart.o
HAL_LIN_SendBreak                                 objs/stm32l4xx_hal_uart.o
                                                  objs/serial_device.o
HAL_MPU_ConfigRegion                              objs/stm32l4xx_hal_cortex.o
HAL_MPU_Disable                                   objs/stm32l4xx_hal_cortex.o
HAL_MPU_Enable                                    objs/stm32l4xx_hal_cortex.o
HAL_MspDeInit                                     objs/stm32l4xx_hal.o
HAL_MspInit                                       objs/stm32l4xx_hal.o
HAL_MultiProcessorEx_AddressLength_Set            objs/stm32l4xx_hal_uart_ex.o
HAL_MultiProcessor_DisableMuteMode                objs/stm32l4xx_hal_uart.o
HAL_MultiProcessor_EnableMuteMode                 objs/stm32l4xx_hal_uart.o
HAL_MultiProcessor_EnterMuteMode                  objs/stm32l4xx_hal_uart.o
HAL_MultiProcessor_Init                           objs/stm32l4xx_hal_uart.o
HAL_NVIC_ClearPendingIRQ                          objs/stm32l4xx_hal_cortex.o
HAL_NVIC_DisableIRQ                               objs/stm32l4xx_hal_cortex.o
                                                  objs/i2c_api.o
HAL_NVIC_EnableIRQ                                objs/stm32l4xx_hal_cortex.o
HAL_NVIC_GetActive                                objs/stm32l4xx_hal_cortex.o
HAL_NVIC_GetPendingIRQ                            objs/stm32l4xx_hal_cortex.o
HAL_NVIC_GetPriority                              objs/stm32l4xx_hal_cortex.o
HAL_NVIC_GetPriorityGrouping                      objs/stm32l4xx_hal_cortex.o
HAL_NVIC_SetPendingIRQ                            objs/stm32l4xx_hal_cortex.o
HAL_NVIC_SetPriority                              objs/stm32l4xx_hal_cortex.o
                                                  objs/stm32l4xx_hal.o
HAL_NVIC_SetPriorityGrouping                      objs/stm32l4xx_hal_cortex.o
                                                  objs/stm32l4xx_hal.o
HAL_NVIC_SystemReset                              objs/stm32l4xx_hal_cortex.o
HAL_PWREx_ConfigPVM                               objs/stm32l4xx_hal_pwr_ex.o
HAL_PWREx_ControlVoltageScaling                   objs/stm32l4xx_hal_pwr_ex.o
HAL_PWREx_DisableBatteryCharging                  objs/stm32l4xx_hal_pwr_ex.o
HAL_PWREx_DisableGPIOPullDown                     objs/stm32l4xx_hal_pwr_ex.o
HAL_PWREx_DisableGPIOPullUp                       objs/stm32l4xx_hal_pwr_ex.o
HAL_PWREx_DisableInternalWakeUpLine               objs/stm32l4xx_hal_pwr_ex.o
HAL_PWREx_DisableLowPowerRunMode                  objs/stm32l4xx_hal_pwr_ex.o
                                                  objs/stm32l4xx_hal_pwr.o
HAL_PWREx_DisablePVM3                             objs/stm32l4xx_hal_pwr_ex.o
HAL_PWREx_DisablePVM4                             objs/stm32l4xx_hal_pwr_ex.o
HAL_PWREx_DisablePullUpPullDownConfig             objs/stm32l4xx_hal_pwr_ex.o
HAL_PWREx_DisableSRAM2ContentRetention            objs/stm32l4xx_hal_pwr_ex.o
HAL_PWREx_EnableBatteryCharging                   objs/stm32l4xx_hal_pwr_ex.o
HAL_PWREx_EnableGPIOPullDown                      objs/stm32l4xx_hal_pwr_ex.o
HAL_PWREx_EnableGPIOPullUp                        objs/stm32l4xx_hal_pwr_ex.o
HAL_PWREx_EnableInternalWakeUpLine                objs/stm32l4xx_hal_pwr_ex.o
HAL_PWREx_EnableLowPowerRunMode                   objs/stm32l4xx_hal_pwr_ex.o
                                                  objs/stm32l4xx_hal_pwr.o
HAL_PWREx_EnablePVM3                              objs/stm32l4xx_hal_pwr_ex.o
HAL_PWREx_EnablePVM4                              objs/stm32l4xx_hal_pwr_ex.o
HAL_PWREx_EnablePullUpPullDownConfig              objs/stm32l4xx_hal_pwr_ex.o
HAL_PWREx_EnableSRAM2ContentRetention             objs/stm32l4xx_hal_pwr_ex.o
HAL_PWREx_EnterSHUTDOWNMode                       objs/stm32l4xx_hal_pwr_ex.o
HAL_PWREx_EnterSTOP0Mode                          objs/stm32l4xx_hal_pwr_ex.o
                                                  objs/stm32l4xx_hal_pwr.o
HAL_PWREx_EnterSTOP1Mode                          objs/stm32l4xx_hal_pwr_ex.o
                                                  objs/stm32l4xx_hal_pwr.o
HAL_PWREx_EnterSTOP2Mode                          objs/stm32l4xx_hal_pwr_ex.o
HAL_PWREx_GetVoltageRange                         objs/stm32l4xx_hal_pwr_ex.o
                                                  objs/stm32l4xx_hal_rcc.o
HAL_PWREx_PVD_PVM_IRQHandler                      objs/stm32l4xx_hal_pwr_ex.o
HAL_PWREx_PVM3Callback                            objs/stm32l4xx_hal_pwr_ex.o
HAL_PWREx_PVM4Callback                            objs/stm32l4xx_hal_pwr_ex.o
HAL_PWREx_SetSRAM2ContentRetention                objs/stm32l4xx_hal_pwr_ex.o
HAL_PWR_ConfigPVD                                 objs/stm32l4xx_hal_pwr.o
HAL_PWR_DeInit                                    objs/stm32l4xx_hal_pwr.o
HAL_PWR_DisableBkUpAccess                         objs/stm32l4xx_hal_pwr.o
                                                  objs/stm32l4xx_hal_rcc_ex.o
HAL_PWR_DisablePVD                                objs/stm32l4xx_hal_pwr.o
HAL_PWR_DisableSEVOnPend                          objs/stm32l4xx_hal_pwr.o
HAL_PWR_DisableSleepOnExit                        objs/stm32l4xx_hal_pwr.o
HAL_PWR_DisableWakeUpPin                          objs/stm32l4xx_hal_pwr.o
HAL_PWR_EnableBkUpAccess                          objs/stm32l4xx_hal_pwr.o
                                                  objs/stm32l4xx_hal_rcc_ex.o
                                                  objs/system_file.o
                                                  objs/rtc_api.o
HAL_PWR_EnablePVD                                 objs/stm32l4xx_hal_pwr.o
HAL_PWR_EnableSEVOnPend                           objs/stm32l4xx_hal_pwr.o
HAL_PWR_EnableSleepOnExit                         objs/stm32l4xx_hal_pwr.o
HAL_PWR_EnableWakeUpPin                           objs/stm32l4xx_hal_pwr.o
HAL_PWR_EnterSLEEPMode                            objs/stm32l4xx_hal_pwr.o
HAL_PWR_EnterSTANDBYMode                          objs/stm32l4xx_hal_pwr.o
HAL_PWR_EnterSTOPMode                             objs/stm32l4xx_hal_pwr.o
HAL_PWR_PVDCallback                               objs/stm32l4xx_hal_pwr.o
                                                  objs/stm32l4xx_hal_pwr_ex.o
HAL_RCCEx_CRSConfig                               objs/stm32l4xx_hal_rcc_ex.o
HAL_RCCEx_CRSGetSynchronizationInfo               objs/stm32l4xx_hal_rcc_ex.o
HAL_RCCEx_CRSSoftwareSynchronizationGenerate      objs/stm32l4xx_hal_rcc_ex.o
HAL_RCCEx_CRSWaitSynchronization                  objs/stm32l4xx_hal_rcc_ex.o
HAL_RCCEx_CRS_ErrorCallback                       objs/stm32l4xx_hal_rcc_ex.o
HAL_RCCEx_CRS_ExpectedSyncCallback                objs/stm32l4xx_hal_rcc_ex.o
HAL_RCCEx_CRS_IRQHandler                          objs/stm32l4xx_hal_rcc_ex.o
HAL_RCCEx_CRS_SyncOkCallback                      objs/stm32l4xx_hal_rcc_ex.o
HAL_RCCEx_CRS_SyncWarnCallback                    objs/stm32l4xx_hal_rcc_ex.o
HAL_RCCEx_DisableLSCO                             objs/stm32l4xx_hal_rcc_ex.o
HAL_RCCEx_DisableLSECSS                           objs/stm32l4xx_hal_rcc_ex.o
HAL_RCCEx_DisableMSIPLLMode                       objs/stm32l4xx_hal_rcc_ex.o
HAL_RCCEx_DisablePLLSAI1                          objs/stm32l4xx_hal_rcc_ex.o
HAL_RCCEx_EnableLSCO                              objs/stm32l4xx_hal_rcc_ex.o
HAL_RCCEx_EnableLSECSS                            objs/stm32l4xx_hal_rcc_ex.o
HAL_RCCEx_EnableLSECSS_IT                         objs/stm32l4xx_hal_rcc_ex.o
HAL_RCCEx_EnableMSIPLLMode                        objs/stm32l4xx_hal_rcc_ex.o
HAL_RCCEx_EnablePLLSAI1                           objs/stm32l4xx_hal_rcc_ex.o
HAL_RCCEx_GetPeriphCLKConfig                      objs/stm32l4xx_hal_rcc_ex.o
HAL_RCCEx_GetPeriphCLKFreq                        objs/stm32l4xx_hal_rcc_ex.o
HAL_RCCEx_LSECSS_Callback                         objs/stm32l4xx_hal_rcc_ex.o
HAL_RCCEx_LSECSS_IRQHandler                       objs/stm32l4xx_hal_rcc_ex.o
HAL_RCCEx_PeriphCLKConfig                         objs/stm32l4xx_hal_rcc_ex.o
                                                  objs/serial_api.o
                                                  objs/rtc_api.o
HAL_RCCEx_StandbyMSIRangeConfig                   objs/stm32l4xx_hal_rcc_ex.o
HAL_RCCEx_WakeUpStopCLKConfig                     objs/stm32l4xx_hal_rcc_ex.o
HAL_RCC_CSSCallback                               objs/stm32l4xx_hal_rcc.o
HAL_RCC_ClockConfig                               objs/stm32l4xx_hal_rcc.o
HAL_RCC_DeInit                                    objs/stm32l4xx_hal_rcc.o
HAL_RCC_EnableCSS                                 objs/stm32l4xx_hal_rcc.o
HAL_RCC_GetClockConfig                            objs/stm32l4xx_hal_rcc.o
                                                  objs/us_ticker.o
HAL_RCC_GetHCLKFreq                               objs/stm32l4xx_hal_rcc.o
HAL_RCC_GetOscConfig                              objs/stm32l4xx_hal_rcc.o
HAL_RCC_GetPCLK1Freq                              objs/stm32l4xx_hal_rcc.o
                                                  objs/stm32l4xx_hal_uart.o
                                                  objs/stm32l4xx_hal_rcc_ex.o
                                                  objs/us_ticker.o
HAL_RCC_GetPCLK2Freq                              objs/stm32l4xx_hal_rcc.o
                                                  objs/stm32l4xx_hal_uart.o
                                                  objs/stm32l4xx_hal_rcc_ex.o
HAL_RCC_GetSysClockFreq                           objs/stm32l4xx_hal_rcc.o
                                                  objs/stm32l4xx_hal_uart.o
                                                  objs/stm32l4xx_hal_rcc_ex.o
HAL_RCC_MCOConfig                                 objs/stm32l4xx_hal_rcc.o
HAL_RCC_NMI_IRQHandler                            objs/stm32l4xx_hal_rcc.o
HAL_RCC_OscConfig                                 objs/stm32l4xx_hal_rcc.o
                                                  objs/rtc_api.o
HAL_RS485Ex_Init                                  objs/stm32l4xx_hal_uart_ex.o
HAL_RTCEx_AlarmBEventCallback                     objs/stm32l4xx_hal_rtc_ex.o
                                                  objs/stm32l4xx_hal_rtc.o
HAL_RTCEx_BKUPRead                                objs/stm32l4xx_hal_rtc_ex.o
                                                  objs/system_file.o
HAL_RTCEx_BKUPWrite                               objs/stm32l4xx_hal_rtc_ex.o
                                                  objs/system_file.o
HAL_RTCEx_DeactivateCalibrationOutPut             objs/stm32l4xx_hal_rtc_ex.o
HAL_RTCEx_DeactivateInternalTimeStamp             objs/stm32l4xx_hal_rtc_ex.o
HAL_RTCEx_DeactivateRefClock                      objs/stm32l4xx_hal_rtc_ex.o
HAL_RTCEx_DeactivateTamper                        objs/stm32l4xx_hal_rtc_ex.o
HAL_RTCEx_DeactivateTimeStamp                     objs/stm32l4xx_hal_rtc_ex.o
HAL_RTCEx_DeactivateWakeUpTimer                   objs/stm32l4xx_hal_rtc_ex.o
HAL_RTCEx_DisableBypassShadow                     objs/stm32l4xx_hal_rtc_ex.o
HAL_RTCEx_EnableBypassShadow                      objs/stm32l4xx_hal_rtc_ex.o
                                                  objs/rtc_api.o
HAL_RTCEx_GetTimeStamp                            objs/stm32l4xx_hal_rtc_ex.o
HAL_RTCEx_GetWakeUpTimer                          objs/stm32l4xx_hal_rtc_ex.o
HAL_RTCEx_PollForAlarmBEvent                      objs/stm32l4xx_hal_rtc_ex.o
HAL_RTCEx_PollForTamper1Event                     objs/stm32l4xx_hal_rtc_ex.o
HAL_RTCEx_PollForTamper2Event                     objs/stm32l4xx_hal_rtc_ex.o
HAL_RTCEx_PollForTamper3Event                     objs/stm32l4xx_hal_rtc_ex.o
HAL_RTCEx_PollForTimeStampEvent                   objs/stm32l4xx_hal_rtc_ex.o
HAL_RTCEx_PollForWakeUpTimerEvent                 objs/stm32l4xx_hal_rtc_ex.o
HAL_RTCEx_SetCalibrationOutPut                    objs/stm32l4xx_hal_rtc_ex.o
HAL_RTCEx_SetInternalTimeStamp                    objs/stm32l4xx_hal_rtc_ex.o
HAL_RTCEx_SetRefClock                             objs/stm32l4xx_hal_rtc_ex.o
HAL_RTCEx_SetSmoothCalib                          objs/stm32l4xx_hal_rtc_ex.o
HAL_RTCEx_SetSynchroShift                         objs/stm32l4xx_hal_rtc_ex.o
HAL_RTCEx_SetTamper                               objs/stm32l4xx_hal_rtc_ex.o
HAL_RTCEx_SetTamper_IT                            objs/stm32l4xx_hal_rtc_ex.o
HAL_RTCEx_SetTimeStamp                            objs/stm32l4xx_hal_rtc_ex.o
HAL_RTCEx_SetTimeStamp_IT                         objs/stm32l4xx_hal_rtc_ex.o
HAL_RTCEx_SetWakeUpTimer                          objs/stm32l4xx_hal_rtc_ex.o
HAL_RTCEx_SetWakeUpTimer_IT                       objs/stm32l4xx_hal_rtc_ex.o
HAL_RTCEx_Tamper1EventCallback                    objs/stm32l4xx_hal_rtc_ex.o
HAL_RTCEx_Tamper2EventCallback                    objs/stm32l4xx_hal_rtc_ex.o
HAL_RTCEx_Tamper3EventCallback                    objs/stm32l4xx_hal_rtc_ex.o
HAL_RTCEx_TamperTimeStampIRQHandler               objs/stm32l4xx_hal_rtc_ex.o
HAL_RTCEx_TimeStampEventCallback                  objs/stm32l4xx_hal_rtc_ex.o
HAL_RTCEx_WakeUpTimerEventCallback                objs/stm32l4xx_hal_rtc_ex.o
HAL_RTCEx_WakeUpTimerIRQHandler                   objs/stm32l4xx_hal_rtc_ex.o
HAL_RTC_AlarmAEventCallback                       objs/stm32l4xx_hal_rtc.o
HAL_RTC_AlarmIRQHandler                           objs/stm32l4xx_hal_rtc.o
HAL_RTC_DST_Add1Hour                              objs/stm32l4xx_hal_rtc.o
HAL_RTC_DST_ClearStoreOperation                   objs/stm32l4xx_hal_rtc.o
HAL_RTC_DST_ReadStoreOperation                    objs/stm32l4xx_hal_rtc.o
HAL_RTC_DST_SetStoreOperation                     objs/stm32l4xx_hal_rtc.o
HAL_RTC_DST_Sub1Hour                              objs/stm32l4xx_hal_rtc.o
HAL_RTC_DeInit                                    objs/stm32l4xx_hal_rtc.o
HAL_RTC_DeactivateAlarm                           objs/stm32l4xx_hal_rtc.o
HAL_RTC_GetAlarm                                  objs/stm32l4xx_hal_rtc.o
HAL_RTC_GetDate                                   objs/stm32l4xx_hal_rtc.o
HAL_RTC_GetState                                  objs/stm32l4xx_hal_rtc.o
HAL_RTC_GetTime                                   objs/stm32l4xx_hal_rtc.o
HAL_RTC_Init                                      objs/stm32l4xx_hal_rtc.o
                                                  objs/rtc_api.o
HAL_RTC_MspDeInit                                 objs/stm32l4xx_hal_rtc.o
HAL_RTC_MspInit                                   objs/stm32l4xx_hal_rtc.o
HAL_RTC_PollForAlarmAEvent                        objs/stm32l4xx_hal_rtc.o
HAL_RTC_SetAlarm                                  objs/stm32l4xx_hal_rtc.o
HAL_RTC_SetAlarm_IT                               objs/stm32l4xx_hal_rtc.o
HAL_RTC_SetDate                                   objs/stm32l4xx_hal_rtc.o
                                                  objs/rtc_api.o
HAL_RTC_SetTime                                   objs/stm32l4xx_hal_rtc.o
                                                  objs/rtc_api.o
HAL_RTC_WaitForSynchro                            objs/stm32l4xx_hal_rtc.o
                                                  objs/stm32l4xx_hal_rtc_ex.o
HAL_ResumeTick                                    objs/stm32l4xx_hal.o
HAL_SYSCFG_DisableIOAnalogSwitchBooster           objs/stm32l4xx_hal.o
HAL_SYSCFG_DisableMemorySwappingBank              objs/stm32l4xx_hal.o
HAL_SYSCFG_DisableVREFBUF                         objs/stm32l4xx_hal.o
HAL_SYSCFG_EnableIOAnalogSwitchBooster            objs/stm32l4xx_hal.o
HAL_SYSCFG_EnableMemorySwappingBank               objs/stm32l4xx_hal.o
HAL_SYSCFG_EnableVREFBUF                          objs/stm32l4xx_hal.o
HAL_SYSCFG_SRAM2Erase                             objs/stm32l4xx_hal.o
HAL_SYSCFG_VREFBUF_HighImpedanceConfig            objs/stm32l4xx_hal.o
HAL_SYSCFG_VREFBUF_TrimmingConfig                 objs/stm32l4xx_hal.o
HAL_SYSCFG_VREFBUF_VoltageScalingConfig           objs/stm32l4xx_hal.o
HAL_SYSTICK_CLKSourceConfig                       objs/stm32l4xx_hal_cortex.o
HAL_SYSTICK_Callback                              objs/stm32l4xx_hal_cortex.o
HAL_SYSTICK_Config                                objs/stm32l4xx_hal_cortex.o
                                                  objs/stm32l4xx_hal.o
HAL_SYSTICK_IRQHandler                            objs/stm32l4xx_hal_cortex.o
HAL_SetTickFreq                                   objs/stm32l4xx_hal.o
HAL_SuspendTick                                   objs/stm32l4xx_hal.o
HAL_TIMEx_Break2Callback                          objs/stm32l4xx_hal_tim.o
HAL_TIMEx_BreakCallback                           objs/stm32l4xx_hal_tim.o
HAL_TIMEx_CommutCallback                          objs/stm32l4xx_hal_tim.o
HAL_TIM_Base_DeInit                               objs/stm32l4xx_hal_tim.o
HAL_TIM_Base_GetState                             objs/stm32l4xx_hal_tim.o
HAL_TIM_Base_Init                                 objs/stm32l4xx_hal_tim.o
HAL_TIM_Base_MspDeInit                            objs/stm32l4xx_hal_tim.o
HAL_TIM_Base_MspInit                              objs/stm32l4xx_hal_tim.o
HAL_TIM_Base_Start                                objs/stm32l4xx_hal_tim.o
HAL_TIM_Base_Start_DMA                            objs/stm32l4xx_hal_tim.o
HAL_TIM_Base_Start_IT                             objs/stm32l4xx_hal_tim.o
HAL_TIM_Base_Stop                                 objs/stm32l4xx_hal_tim.o
HAL_TIM_Base_Stop_DMA                             objs/stm32l4xx_hal_tim.o
HAL_TIM_Base_Stop_IT                              objs/stm32l4xx_hal_tim.o
HAL_TIM_ConfigClockSource                         objs/stm32l4xx_hal_tim.o
HAL_TIM_ConfigOCrefClear                          objs/stm32l4xx_hal_tim.o
HAL_TIM_ConfigTI1Input                            objs/stm32l4xx_hal_tim.o
HAL_TIM_DMABurstState                             objs/stm32l4xx_hal_tim.o
HAL_TIM_DMABurst_MultiReadStart                   objs/stm32l4xx_hal_tim.o
HAL_TIM_DMABurst_MultiWriteStart                  objs/stm32l4xx_hal_tim.o
HAL_TIM_DMABurst_ReadStart                        objs/stm32l4xx_hal_tim.o
HAL_TIM_DMABurst_ReadStop                         objs/stm32l4xx_hal_tim.o
HAL_TIM_DMABurst_WriteStart                       objs/stm32l4xx_hal_tim.o
HAL_TIM_DMABurst_WriteStop                        objs/stm32l4xx_hal_tim.o
HAL_TIM_Encoder_DeInit                            objs/stm32l4xx_hal_tim.o
HAL_TIM_Encoder_GetState                          objs/stm32l4xx_hal_tim.o
HAL_TIM_Encoder_Init                              objs/stm32l4xx_hal_tim.o
HAL_TIM_Encoder_MspDeInit                         objs/stm32l4xx_hal_tim.o
HAL_TIM_Encoder_MspInit                           objs/stm32l4xx_hal_tim.o
HAL_TIM_Encoder_Start                             objs/stm32l4xx_hal_tim.o
HAL_TIM_Encoder_Start_DMA                         objs/stm32l4xx_hal_tim.o
HAL_TIM_Encoder_Start_IT                          objs/stm32l4xx_hal_tim.o
HAL_TIM_Encoder_Stop                              objs/stm32l4xx_hal_tim.o
HAL_TIM_Encoder_Stop_DMA                          objs/stm32l4xx_hal_tim.o
HAL_TIM_Encoder_Stop_IT                           objs/stm32l4xx_hal_tim.o
HAL_TIM_ErrorCallback                             objs/stm32l4xx_hal_tim.o
HAL_TIM_GenerateEvent                             objs/stm32l4xx_hal_tim.o
HAL_TIM_GetActiveChannel                          objs/stm32l4xx_hal_tim.o
HAL_TIM_GetChannelState                           objs/stm32l4xx_hal_tim.o
HAL_TIM_IC_CaptureCallback                        objs/stm32l4xx_hal_tim.o
HAL_TIM_IC_CaptureHalfCpltCallback                objs/stm32l4xx_hal_tim.o
HAL_TIM_IC_ConfigChannel                          objs/stm32l4xx_hal_tim.o
HAL_TIM_IC_DeInit                                 objs/stm32l4xx_hal_tim.o
HAL_TIM_IC_GetState                               objs/stm32l4xx_hal_tim.o
HAL_TIM_IC_Init                                   objs/stm32l4xx_hal_tim.o
HAL_TIM_IC_MspDeInit                              objs/stm32l4xx_hal_tim.o
HAL_TIM_IC_MspInit                                objs/stm32l4xx_hal_tim.o
HAL_TIM_IC_Start                                  objs/stm32l4xx_hal_tim.o
HAL_TIM_IC_Start_DMA                              objs/stm32l4xx_hal_tim.o
HAL_TIM_IC_Start_IT                               objs/stm32l4xx_hal_tim.o
HAL_TIM_IC_Stop                                   objs/stm32l4xx_hal_tim.o
HAL_TIM_IC_Stop_DMA                               objs/stm32l4xx_hal_tim.o
HAL_TIM_IC_Stop_IT                                objs/stm32l4xx_hal_tim.o
HAL_TIM_IRQHandler                                objs/stm32l4xx_hal_tim.o
HAL_TIM_OC_ConfigChannel                          objs/stm32l4xx_hal_tim.o
HAL_TIM_OC_DeInit                                 objs/stm32l4xx_hal_tim.o
HAL_TIM_OC_DelayElapsedCallback                   objs/stm32l4xx_hal_tim.o
HAL_TIM_OC_GetState                               objs/stm32l4xx_hal_tim.o
HAL_TIM_OC_Init                                   objs/stm32l4xx_hal_tim.o
                                                  objs/us_ticker.o
HAL_TIM_OC_MspDeInit                              objs/stm32l4xx_hal_tim.o
HAL_TIM_OC_MspInit                                objs/stm32l4xx_hal_tim.o
HAL_TIM_OC_Start                                  objs/stm32l4xx_hal_tim.o
                                                  objs/us_ticker.o
HAL_TIM_OC_Start_DMA                              objs/stm32l4xx_hal_tim.o
HAL_TIM_OC_Start_IT                               objs/stm32l4xx_hal_tim.o
HAL_TIM_OC_Stop                                   objs/stm32l4xx_hal_tim.o
                                                  objs/us_ticker.o
HAL_TIM_OC_Stop_DMA                               objs/stm32l4xx_hal_tim.o
HAL_TIM_OC_Stop_IT                                objs/stm32l4xx_hal_tim.o
HAL_TIM_OnePulse_ConfigChannel                    objs/stm32l4xx_hal_tim.o
HAL_TIM_OnePulse_DeInit                           objs/stm32l4xx_hal_tim.o
HAL_TIM_OnePulse_GetState                         objs/stm32l4xx_hal_tim.o
HAL_TIM_OnePulse_Init                             objs/stm32l4xx_hal_tim.o
HAL_TIM_OnePulse_MspDeInit                        objs/stm32l4xx_hal_tim.o
HAL_TIM_OnePulse_MspInit                          objs/stm32l4xx_hal_tim.o
HAL_TIM_OnePulse_Start                            objs/stm32l4xx_hal_tim.o
HAL_TIM_OnePulse_Start_IT                         objs/stm32l4xx_hal_tim.o
HAL_TIM_OnePulse_Stop                             objs/stm32l4xx_hal_tim.o
HAL_TIM_OnePulse_Stop_IT                          objs/stm32l4xx_hal_tim.o
HAL_TIM_PWM_ConfigChannel                         objs/stm32l4xx_hal_tim.o
HAL_TIM_PWM_DeInit                                objs/stm32l4xx_hal_tim.o
HAL_TIM_PWM_GetState                              objs/stm32l4xx_hal_tim.o
HAL_TIM_PWM_Init                                  objs/stm32l4xx_hal_tim.o
HAL_TIM_PWM_MspDeInit                             objs/stm32l4xx_hal_tim.o
HAL_TIM_PWM_MspInit                               objs/stm32l4xx_hal_tim.o
HAL_TIM_PWM_PulseFinishedCallback                 objs/stm32l4xx_hal_tim.o
HAL_TIM_PWM_PulseFinishedHalfCpltCallback         objs/stm32l4xx_hal_tim.o
HAL_TIM_PWM_Start                                 objs/stm32l4xx_hal_tim.o
HAL_TIM_PWM_Start_DMA                             objs/stm32l4xx_hal_tim.o
HAL_TIM_PWM_Start_IT                              objs/stm32l4xx_hal_tim.o
HAL_TIM_PWM_Stop                                  objs/stm32l4xx_hal_tim.o
HAL_TIM_PWM_Stop_DMA                              objs/stm32l4xx_hal_tim.o
HAL_TIM_PWM_Stop_IT                               objs/stm32l4xx_hal_tim.o
HAL_TIM_PeriodElapsedCallback                     objs/stm32l4xx_hal_tim.o
HAL_TIM_PeriodElapsedHalfCpltCallback             objs/stm32l4xx_hal_tim.o
HAL_TIM_ReadCapturedValue                         objs/stm32l4xx_hal_tim.o
HAL_TIM_SlaveConfigSynchro                        objs/stm32l4xx_hal_tim.o
HAL_TIM_SlaveConfigSynchro_IT                     objs/stm32l4xx_hal_tim.o
HAL_TIM_TriggerCallback                           objs/stm32l4xx_hal_tim.o
HAL_TIM_TriggerHalfCpltCallback                   objs/stm32l4xx_hal_tim.o
HAL_UARTEx_DisableClockStopMode                   objs/stm32l4xx_hal_uart_ex.o
                                                  objs/serial_api.o
HAL_UARTEx_DisableStopMode                        objs/stm32l4xx_hal_uart_ex.o
                                                  objs/serial_api.o
HAL_UARTEx_EnableClockStopMode                    objs/stm32l4xx_hal_uart_ex.o
HAL_UARTEx_EnableStopMode                         objs/stm32l4xx_hal_uart_ex.o
                                                  objs/serial_api.o
HAL_UARTEx_ReceiveToIdle                          objs/stm32l4xx_hal_uart_ex.o
HAL_UARTEx_ReceiveToIdle_DMA                      objs/stm32l4xx_hal_uart_ex.o
HAL_UARTEx_ReceiveToIdle_IT                       objs/stm32l4xx_hal_uart_ex.o
HAL_UARTEx_RxEventCallback                        objs/stm32l4xx_hal_uart.o
HAL_UARTEx_StopModeWakeUpSourceConfig             objs/stm32l4xx_hal_uart_ex.o
HAL_UARTEx_WakeupCallback                         objs/stm32l4xx_hal_uart_ex.o
                                                  objs/stm32l4xx_hal_uart.o
HAL_UART_Abort                                    objs/stm32l4xx_hal_uart.o
HAL_UART_AbortCpltCallback                        objs/stm32l4xx_hal_uart.o
HAL_UART_AbortReceive                             objs/stm32l4xx_hal_uart.o
HAL_UART_AbortReceiveCpltCallback                 objs/stm32l4xx_hal_uart.o
HAL_UART_AbortReceive_IT                          objs/stm32l4xx_hal_uart.o
HAL_UART_AbortTransmit                            objs/stm32l4xx_hal_uart.o
HAL_UART_AbortTransmitCpltCallback                objs/stm32l4xx_hal_uart.o
HAL_UART_AbortTransmit_IT                         objs/stm32l4xx_hal_uart.o
HAL_UART_Abort_IT                                 objs/stm32l4xx_hal_uart.o
HAL_UART_DMAPause                                 objs/stm32l4xx_hal_uart.o
HAL_UART_DMAResume                                objs/stm32l4xx_hal_uart.o
HAL_UART_DMAStop                                  objs/stm32l4xx_hal_uart.o
HAL_UART_DeInit                                   objs/stm32l4xx_hal_uart.o
HAL_UART_DisableReceiverTimeout                   objs/stm32l4xx_hal_uart.o
HAL_UART_EnableReceiverTimeout                    objs/stm32l4xx_hal_uart.o
HAL_UART_ErrorCallback                            objs/serial_device.o
HAL_UART_GetError                                 objs/stm32l4xx_hal_uart.o
HAL_UART_GetState                                 objs/stm32l4xx_hal_uart.o
                                                  objs/serial_device.o
HAL_UART_IRQHandler                               objs/stm32l4xx_hal_uart.o
                                                  objs/serial_device.o
HAL_UART_Init                                     objs/stm32l4xx_hal_uart.o
                                                  objs/serial_api.o
HAL_UART_MspDeInit                                objs/stm32l4xx_hal_uart.o
HAL_UART_MspInit                                  objs/stm32l4xx_hal_uart.o
                                                  objs/stm32l4xx_hal_uart_ex.o
HAL_UART_Receive                                  objs/stm32l4xx_hal_uart.o
                                                  objs/serial_api_stubs.o
HAL_UART_Receive_DMA                              objs/stm32l4xx_hal_uart.o
HAL_UART_Receive_IT                               objs/stm32l4xx_hal_uart.o
                                                  objs/serial_device.o
HAL_UART_ReceiverTimeout_Config                   objs/stm32l4xx_hal_uart.o
HAL_UART_RxCpltCallback                           objs/stm32l4xx_hal_uart.o
HAL_UART_RxHalfCpltCallback                       objs/stm32l4xx_hal_uart.o
HAL_UART_Transmit                                 objs/stm32l4xx_hal_uart.o
                                                  objs/serial_api_stubs.o
HAL_UART_Transmit_DMA                             objs/stm32l4xx_hal_uart.o
HAL_UART_Transmit_IT                              objs/stm32l4xx_hal_uart.o
                                                  objs/serial_device.o
HAL_UART_TxCpltCallback                           objs/stm32l4xx_hal_uart.o
HAL_UART_TxHalfCpltCallback                       objs/stm32l4xx_hal_uart.o
HardFault_Handler                                 objs/board_fault.o
                                                  objs/startup_stm32l431xx.o
I2C1_ER_IRQHandler                                objs/startup_stm32l431xx.o
I2C1_EV_IRQHandler                                objs/startup_stm32l431xx.o
I2C2_ER_IRQHandler                                objs/startup_stm32l431xx.o
I2C2_EV_IRQHandler                                objs/startup_stm32l431xx.o
I2C3_ER_IRQHandler                                objs/startup_stm32l431xx.o
I2C3_EV_IRQHandler                                objs/startup_stm32l431xx.o
ITM_Printf                                        objs/serial_wire_debug.o
ITM_SendString                                    objs/serial_wire_debug.o
ITM_SendStringSized                               objs/serial_wire_debug.o
                                                  objs/console.o
LPTIM1_IRQHandler                                 objs/startup_stm32l431xx.o
LPTIM2_IRQHandler                                 objs/startup_stm32l431xx.o
LPUART1_IRQHandler                                objs/startup_stm32l431xx.o
MSIRangeTable                                     objs/system_stm32l4xx.o
                                                  objs/stm32l4xx_hal_rcc_ex.o
                                                  objs/stm32l4xx_hal_rcc.o
MemManage_Handler                                 objs/startup_stm32l431xx.o
NMI_Handler                                       objs/startup_stm32l431xx.o
OS_Tick_Handler                                   objs/HAL_CM4.o
PVD_PVM_IRQHandler                                objs/startup_stm32l431xx.o
PendSV_Handler                                    objs/HAL_CM4.o
                                                  objs/startup_stm32l431xx.o
PinMap_ADC                                        objs/PeripheralPins.o
                                                  objs/analogin_device.o
PinMap_ADC_Internal                               objs/PeripheralPins.o
                                                  objs/analogin_device.o
PinMap_CAN_RD                                     objs/PeripheralPins.o
PinMap_CAN_TD                                     objs/PeripheralPins.o
PinMap_DAC                                        objs/PeripheralPins.o
PinMap_I2C_SCL                                    objs/PeripheralPins.o
                                                  objs/i2c_api.o
PinMap_I2C_SDA                                    objs/PeripheralPins.o
                                                  objs/i2c_api.o
PinMap_PWM                                        objs/PeripheralPins.o
PinMap_QSPI_DATA0                                 objs/PeripheralPins.o
PinMap_QSPI_DATA1                                 objs/PeripheralPins.o
PinMap_QSPI_DATA2                                 objs/PeripheralPins.o
PinMap_QSPI_DATA3                                 objs/PeripheralPins.o
PinMap_QSPI_SCLK                                  objs/PeripheralPins.o
PinMap_QSPI_SSEL                                  objs/PeripheralPins.o
PinMap_SPI_MISO                                   objs/PeripheralPins.o
PinMap_SPI_MOSI                                   objs/PeripheralPins.o
PinMap_SPI_SCLK                                   objs/PeripheralPins.o
PinMap_SPI_SSEL                                   objs/PeripheralPins.o
PinMap_UART_CTS                                   objs/PeripheralPins.o
PinMap_UART_RTS                                   objs/PeripheralPins.o
PinMap_UART_RX                                    objs/PeripheralPins.o
                                                  objs/serial_api.o
PinMap_UART_TX                                    objs/PeripheralPins.o
                                                  objs/serial_api.o
QUADSPI_IRQHandler                                objs/startup_stm32l431xx.o
RCC_IRQHandler                                    objs/startup_stm32l431xx.o
RNG_IRQHandler                                    objs/startup_stm32l431xx.o
RTC_Alarm_IRQHandler                              objs/startup_stm32l431xx.o
RTC_Bcd2ToByte                                    objs/stm32l4xx_hal_rtc.o
                                                  objs/stm32l4xx_hal_rtc_ex.o
                                                  objs/rtc_api.o
RTC_ByteToBcd2                                    objs/stm32l4xx_hal_rtc.o
RTC_EnterInitMode                                 objs/stm32l4xx_hal_rtc.o
                                                  objs/stm32l4xx_hal_rtc_ex.o
RTC_ExitInitMode                                  objs/stm32l4xx_hal_rtc.o
                                                  objs/stm32l4xx_hal_rtc_ex.o
RTC_WKUP_IRQHandler                               objs/startup_stm32l431xx.o
Reset_Handler                                     objs/startup_stm32l431xx.o
SAI1_IRQHandler                                   objs/startup_stm32l431xx.o
SDMMC1_IRQHandler                                 objs/startup_stm32l431xx.o
SPI1_IRQHandler                                   objs/startup_stm32l431xx.o
SPI2_IRQHandler                                   objs/startup_stm32l431xx.o
SPI3_IRQHandler                                   objs/startup_stm32l431xx.o
SVC_Count                                         objs/SVC_Table.o
                                                  objs/HAL_CM4.o
SVC_Handler                                       objs/HAL_CM4.o
                                                  objs/startup_stm32l431xx.o
SVC_Table                                         objs/SVC_Table.o
                                                  objs/HAL_CM4.o
SWO_Init                                          objs/serial_wire_debug.o
SWPMI1_IRQHandler                                 objs/startup_stm32l431xx.o
Set_GPIO_Clock                                    objs/gpio_api.o
                                                  objs/pinmap.o
                                                  objs/gpio_irq_api.o
SysTick_Handler                                   objs/HAL_CM4.o
                                                  objs/startup_stm32l431xx.o
Sys_Switch                                        objs/HAL_CM4.o
SystemCoreClock                                   objs/system_stm32l4xx.o
                                                  objs/stm32l4xx_hal_adc_ex.o
                                                  objs/stm32l4xx_hal_adc.o
                                                  objs/stm32l4xx_hal_rcc.o
                                                  objs/stm32l4xx_hal_pwr_ex.o
                                                  objs/stm32l4xx_hal.o
                                                  objs/i2c_api.o
SystemCoreClockUpdate                             objs/system_stm32l4xx.o
SystemInit                                        objs/system_stm32l4xx.o
                                                  objs/startup_stm32l431xx.o
TAMP_STAMP_IRQHandler                             objs/startup_stm32l431xx.o
TIM1_BRK_TIM15_IRQHandler                         objs/startup_stm32l431xx.o
TIM1_CC_IRQHandler                                objs/startup_stm32l431xx.o
TIM1_TRG_COM_IRQHandler                           objs/startup_stm32l431xx.o
TIM1_UP_TIM16_IRQHandler                          objs/startup_stm32l431xx.o
TIM2_IRQHandler                                   objs/startup_stm32l431xx.o
TIM6_DAC_IRQHandler                               objs/startup_stm32l431xx.o
TIM7_IRQHandler                                   objs/startup_stm32l431xx.o
TIMEx_DMACommutationCplt                          objs/stm32l4xx_hal_tim.o
TIMEx_DMACommutationHalfCplt                      objs/stm32l4xx_hal_tim.o
TIM_Base_SetConfig                                objs/stm32l4xx_hal_tim.o
TIM_CCxChannelCmd                                 objs/stm32l4xx_hal_tim.o
TIM_DMACaptureCplt                                objs/stm32l4xx_hal_tim.o
TIM_DMACaptureHalfCplt                            objs/stm32l4xx_hal_tim.o
TIM_DMADelayPulseHalfCplt                         objs/stm32l4xx_hal_tim.o
TIM_DMAError                                      objs/stm32l4xx_hal_tim.o
TIM_ETR_SetConfig                                 objs/stm32l4xx_hal_tim.o
TIM_OC2_SetConfig                                 objs/stm32l4xx_hal_tim.o
TIM_TI1_SetConfig                                 objs/stm32l4xx_hal_tim.o
TSC_IRQHandler                                    objs/startup_stm32l431xx.o
TimMasterHandle                                   objs/us_ticker.o
UART_AdvFeatureConfig                             objs/stm32l4xx_hal_uart.o
                                                  objs/stm32l4xx_hal_uart_ex.o
UART_CheckIdleState                               objs/stm32l4xx_hal_uart.o
                                                  objs/stm32l4xx_hal_uart_ex.o
UART_SetConfig                                    objs/stm32l4xx_hal_uart.o
                                                  objs/stm32l4xx_hal_uart_ex.o
UART_Start_Receive_DMA                            objs/stm32l4xx_hal_uart.o
                                                  objs/stm32l4xx_hal_uart_ex.o
UART_Start_Receive_IT                             objs/stm32l4xx_hal_uart.o
                                                  objs/stm32l4xx_hal_uart_ex.o
UART_WaitOnFlagUntilTimeout                       objs/stm32l4xx_hal_uart.o
                                                  objs/stm32l4xx_hal_uart_ex.o
USART1_IRQHandler                                 objs/startup_stm32l431xx.o
USART2_IRQHandler                                 objs/startup_stm32l431xx.o
USART3_IRQHandler                                 objs/startup_stm32l431xx.o
UsageFault_Handler                                objs/startup_stm32l431xx.o
WWDG_IRQHandler                                   objs/startup_stm32l431xx.o
__adddf3                                          /usr/lib/gcc/arm-none-eabi/13.2.1/thumb/v7e-m+fp/hard/libgcc.a(_arm_addsubdf3.o)
__aeabi_d2f                                       /usr/lib/gcc/arm-none-eabi/13.2.1/thumb/v7e-m+fp/hard/libgcc.a(_arm_truncdfsf2.o)
                                                  objs/nmea.o
__aeabi_d2iz                                      /usr/lib/gcc/arm-none-eabi/13.2.1/thumb/v7e-m+fp/hard/libgcc.a(_arm_fixdfsi.o)
                                                  objs/datetime.o
__aeabi_dadd                                      /usr/lib/gcc/arm-none-eabi/13.2.1/thumb/v7e-m+fp/hard/libgcc.a(_arm_addsubdf3.o)
                                                  objs/nmea.o
                                                  objs/libc.o
__aeabi_ddiv                                      /usr/lib/gcc/arm-none-eabi/13.2.1/thumb/v7e-m+fp/hard/libgcc.a(_arm_muldivdf3.o)
                                                  objs/nmea.o
                                                  objs/datetime.o
                                                  objs/libc.o
__aeabi_dmul                                      /usr/lib/gcc/arm-none-eabi/13.2.1/thumb/v7e-m+fp/hard/libgcc.a(_arm_muldivdf3.o)
                                                  /usr/lib/gcc/arm-none-eabi/13.2.1/thumb/v7e-m+fp/hard/libgcc.a(_arm_muldf3.o)
                                                  objs/nmea.o
                                                  objs/datetime.o
                                                  objs/libc.o
__aeabi_drsub                                     /usr/lib/gcc/arm-none-eabi/13.2.1/thumb/v7e-m+fp/hard/libgcc.a(_arm_addsubdf3.o)
__aeabi_dsub                                      /usr/lib/gcc/arm-none-eabi/13.2.1/thumb/v7e-m+fp/hard/libgcc.a(_arm_addsubdf3.o)
                                                  objs/nmea.o
__aeabi_f2d                                       /usr/lib/gcc/arm-none-eabi/13.2.1/thumb/v7e-m+fp/hard/libgcc.a(_arm_addsubdf3.o)
                                                  objs/nmea.o
__aeabi_i2d                                       /usr/lib/gcc/arm-none-eabi/13.2.1/thumb/v7e-m+fp/hard/libgcc.a(_arm_addsubdf3.o)
                                                  objs/nmea.o
                                                  objs/libc.o
__aeabi_idiv0                                     /usr/lib/gcc/arm-none-eabi/13.2.1/thumb/v7e-m+fp/hard/libgcc.a(_dvmd_tls.o)
__aeabi_l2d                                       /usr/lib/gcc/arm-none-eabi/13.2.1/thumb/v7e-m+fp/hard/libgcc.a(_arm_addsubdf3.o)
__aeabi_ldiv0                                     /usr/lib/gcc/arm-none-eabi/13.2.1/thumb/v7e-m+fp/hard/libgcc.a(_dvmd_tls.o)
                                                  /usr/lib/gcc/arm-none-eabi/13.2.1/thumb/v7e-m+fp/hard/libgcc.a(_aeabi_uldivmod.o)
__aeabi_ui2d                                      /usr/lib/gcc/arm-none-eabi/13.2.1/thumb/v7e-m+fp/hard/libgcc.a(_arm_addsubdf3.o)
__aeabi_ul2d                                      /usr/lib/gcc/arm-none-eabi/13.2.1/thumb/v7e-m+fp/hard/libgcc.a(_arm_addsubdf3.o)
__aeabi_uldivmod                                  /usr/lib/gcc/arm-none-eabi/13.2.1/thumb/v7e-m+fp/hard/libgcc.a(_aeabi_uldivmod.o)
                                                  objs/stm32l4xx_hal_uart.o
                                                  objs/rt_CMSIS.o
__divdf3                                          /usr/lib/gcc/arm-none-eabi/13.2.1/thumb/v7e-m+fp/hard/libgcc.a(_arm_muldivdf3.o)
__errno                                           objs/libc.o
__extendsfdf2                                     /usr/lib/gcc/arm-none-eabi/13.2.1/thumb/v7e-m+fp/hard/libgcc.a(_arm_addsubdf3.o)
__fixdfsi                                         /usr/lib/gcc/arm-none-eabi/13.2.1/thumb/v7e-m+fp/hard/libgcc.a(_arm_fixdfsi.o)
__floatdidf                                       /usr/lib/gcc/arm-none-eabi/13.2.1/thumb/v7e-m+fp/hard/libgcc.a(_arm_addsubdf3.o)
__floatsidf                                       /usr/lib/gcc/arm-none-eabi/13.2.1/thumb/v7e-m+fp/hard/libgcc.a(_arm_addsubdf3.o)
__floatundidf                                     /usr/lib/gcc/arm-none-eabi/13.2.1/thumb/v7e-m+fp/hard/libgcc.a(_arm_addsubdf3.o)
__floatunsidf                                     /usr/lib/gcc/arm-none-eabi/13.2.1/thumb/v7e-m+fp/hard/libgcc.a(_arm_addsubdf3.o)
__libc_fini_array                                 objs/RTX_Conf_CM.o
__libc_init_array                                 objs/RTX_Conf_CM.o
__muldf3                                          /usr/lib/gcc/arm-none-eabi/13.2.1/thumb/v7e-m+fp/hard/libgcc.a(_arm_muldivdf3.o)
                                                  /usr/lib/gcc/arm-none-eabi/13.2.1/thumb/v7e-m+fp/hard/libgcc.a(_arm_muldf3.o)
__subdf3                                          /usr/lib/gcc/arm-none-eabi/13.2.1/thumb/v7e-m+fp/hard/libgcc.a(_arm_addsubdf3.o)
__truncdfsf2                                      /usr/lib/gcc/arm-none-eabi/13.2.1/thumb/v7e-m+fp/hard/libgcc.a(_arm_truncdfsf2.o)
__udivmoddi4                                      /usr/lib/gcc/arm-none-eabi/13.2.1/thumb/v7e-m+fp/hard/libgcc.a(_udivmoddi4.o)
                                                  /usr/lib/gcc/arm-none-eabi/13.2.1/thumb/v7e-m+fp/hard/libgcc.a(_aeabi_uldivmod.o)
_alloc_box                                        objs/HAL_CM4.o
                                                  objs/rt_MemBox.o
_calloc_box                                       objs/rt_MemBox.o
_ebss                                             objs/startup_stm32l431xx.o
_edata                                            objs/startup_stm32l431xx.o
_estack                                           objs/startup_stm32l431xx.o
_free_box                                         objs/HAL_CM4.o
_init_box                                         objs/rt_MemBox.o
                                                  objs/rt_Task.o
                                                  objs/rt_CMSIS.o
_sbss                                             objs/startup_stm32l431xx.o
_sdata                                            objs/startup_stm32l431xx.o
_sidata                                           objs/startup_stm32l431xx.o
_start                                            objs/start.o
                                                  objs/startup_stm32l431xx.o
_us_ticker_initialized                            objs/mbed_us_ticker_api.o
abs                                               objs/libc.o
adc_read                                          objs/analogin_device.o
                                                  objs/analogin_api.o
alarm_cancel                                      objs/alarm.o
alarm_head                                        objs/alarm.o
alarm_remain                                      objs/alarm.o
alarm_start                                       objs/alarm.o
alarm_start_common                                objs/alarm.o
alarm_start_periodic                              objs/alarm.o
alarm_start_set                                   objs/alarm.o
alarm_tick                                        objs/alarm.o
analogin_init                                     objs/analogin_device.o
analogin_pinmap                                   objs/analogin_device.o
analogin_read                                     objs/analogin_api.o
analogin_read_u16                                 objs/analogin_api.o
announce                                          objs/announce.o
atexit                                            objs/RTX_Conf_CM.o
atof                                              objs/libc.o
                                                  objs/nmea.o
atoi                                              objs/libc.o
                                                  objs/nmea.o
                                                  objs/event.o
board_fault                                       objs/board_fault.o
                                                  objs/printf.o
board_fault_check                                 objs/board_fault.o
board_fault_save                                  objs/board_fault.o
board_initted                                     objs/start.o
                                                  objs/board_fault.o
board_uid                                         objs/uid.o
                                                  objs/settings.o
                                                  objs/announce.o
                                                  objs/libc.o
build_tag                                         objs/settings.o
                                                  objs/announce.o
build_target_version                              objs/announce.o
build_timestamp                                   objs/settings.o
                                                  objs/announce.o
bus_frequency                                     objs/port_stubs.o
                                                  objs/announce.o
chip_uid                                          objs/chip_uid.o
                                                  objs/uid.o
clock_read                                        objs/port_stubs.o
console_available                                 objs/console.o
console_flush                                     objs/console.o
                                                  objs/gps_quectel.o
                                                  objs/printf.o
console_getchar                                   objs/console.o
console_uart                                      objs/serial_api_stubs.o
                                                  objs/console.o
                                                  objs/mcu_sleep.o
                                                  objs/printf.o
console_write                                     objs/console.o
                                                  objs/gps_quectel.o
                                                  objs/printf.o
core_util_critical_section_enter                  objs/port_stubs.o
                                                  objs/gpio_irq_api.o
                                                  objs/rtc_api.o
core_util_critical_section_exit                   objs/port_stubs.o
                                                  objs/gpio_irq_api.o
                                                  objs/rtc_api.o
crc16                                             objs/crc16.o
crc16_table                                       objs/crc16.o
crc16_with_seed                                   objs/crc16.o
date_to_seconds                                   objs/datetime.o
datetime_display                                  objs/datetime.o
                                                  objs/event.o
datetime_to_seconds                               objs/datetime.o
                                                  objs/rtc_api.o
days_in_month                                     objs/datetime.o
days_per_month                                    objs/datetime.o
device_power_all_off                              objs/mcu_sleep.o
die                                               objs/board_fault.o
                                                  objs/printf.o
display_more                                      objs/console.o
epoch_clock                                       objs/port_stubs.o
                                                  objs/alarm.o
error                                             objs/printf.o
                                                  objs/serial_api.o
                                                  objs/mbed_pinmap_common.o
                                                  objs/gpio_irq_api.o
                                                  objs/gpio_api.o
                                                  objs/analogin_device.o
                                                  objs/rtc_api.o
                                                  objs/board_fault.o
event_buffer                                      objs/event.o
event_display                                     objs/event.o
event_display_mutex                               objs/event.o
event_dump                                        objs/event.o
event_entries                                     objs/event.o
event_in                                          objs/event.o
event_init                                        objs/event.o
event_log                                         objs/event.o
                                                  objs/settings.o
                                                  objs/gps_quectel.o
                                                  objs/board_fault.o
event_log_read                                    objs/event.o
event_log_reset                                   objs/event.o
event_out                                         objs/event.o
event_partition                                   objs/gps_serial_test.o
                                                  objs/event.o
event_setting                                     objs/event.o
                                                  objs/settings.o
event_setting_dump                                objs/event.o
                                                  objs/settings.o
event_settings_init                               objs/event.o
                                                  objs/settings.o
event_show                                        objs/event.o
event_store                                       objs/event.o
event_time                                        objs/event.o
event_type_normalize                              objs/event.o
exit                                              objs/RTX_Conf_CM.o
fault_lr                                          objs/board_fault.o
fault_pc                                          objs/board_fault.o
fault_psr                                         objs/board_fault.o
fault_r0                                          objs/board_fault.o
fault_r1                                          objs/board_fault.o
fault_r12                                         objs/board_fault.o
fault_r2                                          objs/board_fault.o
fault_r3                                          objs/board_fault.o
final_power_off                                   objs/mcu_sleep.o
flash_read_block                                  objs/event.o
flash_read_uint16                                 objs/event.o
flash_read_uint32                                 objs/event.o
flash_read_uint8                                  objs/event.o
flash_store_lock                                  objs/gps_serial_test.o
                                                  objs/event.o
flash_store_partial_okay                          objs/gps_serial_test.o
                                                  objs/event.o
flash_store_unlock                                objs/gps_serial_test.o
                                                  objs/event.o
flash_store_write                                 objs/gps_serial_test.o
                                                  objs/event.o
g_pfnVectors                                      objs/startup_stm32l431xx.o
                                                  objs/system_stm32l4xx.o
get_i2c_obj                                       objs/i2c_api.o
get_uart_index                                    objs/serial_api.o
                                                  objs/serial_device.o
get_uint16                                        objs/bits.o
get_uint32                                        objs/bits.o
get_uint64                                        objs/bits.o
get_us_ticker_data                                objs/mbed_us_ticker_api.o
gpio_dir                                          objs/gpio_api.o
                                                  objs/gpio.o
gpio_init                                         objs/gpio_api.o
                                                  objs/gpio.o
gpio_init_in                                      objs/gpio.o
                                                  objs/gps_quectel.o
gpio_init_in_ex                                   objs/gpio.o
gpio_init_inout                                   objs/gpio.o
gpio_init_out                                     objs/gpio.o
                                                  objs/gps_quectel.o
gpio_init_out_ex                                  objs/gpio.o
                                                  objs/gps_quectel.o
gpio_irq_disable                                  objs/gpio_irq_api.o
gpio_irq_enable                                   objs/gpio_irq_api.o
gpio_irq_free                                     objs/gpio_irq_api.o
gpio_irq_init                                     objs/gpio_irq_api.o
gpio_irq_set                                      objs/gpio_irq_api.o
gpio_mode                                         objs/gpio_api.o
                                                  objs/gps_quectel.o
                                                  objs/gpio.o
gpio_set                                          objs/gpio_api.o
gps_data                                          objs/gps_serial_test.o
                                                  objs/gps_quectel.o
gps_en_pin                                        objs/gps_quectel.o
gps_factory_provision                             objs/gps_quectel.o
gps_highres_mode                                  objs/gps_quectel.o
                                                  objs/gps_serial_test.o
gps_init                                          objs/gps_quectel.o
                                                  objs/gps_serial_test.o
gps_initted                                       objs/gps_quectel.o
                                                  objs/gps_serial_test.o
gps_loc                                           objs/gps_serial_test.o
                                                  objs/gps_quectel.o
gps_low_res_mode                                  objs/gps_quectel.o
gps_op                                            objs/gps_serial_test.o
                                                  objs/gps_quectel.o
gps_periodic_mode                                 objs/gps_quectel.o
gps_pin_initted                                   objs/gps_quectel.o
gps_pin_setup                                     objs/gps_quectel.o
gps_port                                          objs/gps_quectel.o
                                                  objs/gps_serial_test.o
gps_power_off                                     objs/gps_quectel.o
gps_power_on                                      objs/gps_quectel.o
gps_power_switch                                  objs/gps_quectel.o
gps_pps_pin                                       objs/gps_quectel.o
gps_reset                                         objs/gps_quectel.o
                                                  objs/gps_serial_test.o
gps_reset_pin                                     objs/gps_quectel.o
gps_rx_buffer                                     objs/gps_quectel.o
gps_send_command                                  objs/gps_quectel.o
                                                  objs/gps_serial_test.o
gps_setting                                       objs/settings.o
gps_settings_dump                                 objs/settings.o
gps_settings_init                                 objs/settings.o
gps_show_bytes                                    objs/gps_serial_test.o
                                                  objs/gps_quectel.o
gps_standby_mode                                  objs/gps_quectel.o
gps_status_pin                                    objs/gps_quectel.o
gps_test_serial_communication                     objs/gps_serial_test.o
gps_tx_buffer                                     objs/gps_quectel.o
gps_vbckp_pin                                     objs/gps_quectel.o
have_esc                                          objs/console.o
hex_dump                                          objs/printf.o
i2c_byte_read                                     objs/i2c_api.o
i2c_byte_write                                    objs/i2c_api.o
i2c_deinit_internal                               objs/i2c_api.o
i2c_ev_err_disable                                objs/i2c_api.o
i2c_ev_err_enable                                 objs/i2c_api.o
i2c_free                                          objs/i2c_api.o
i2c_frequency                                     objs/i2c_api.o
i2c_get_irq_handler                               objs/i2c_api.o
i2c_get_timing                                    objs/i2c_api.o
i2c_hw_reset                                      objs/i2c_api.o
i2c_init                                          objs/i2c_api.o
i2c_init_internal                                 objs/i2c_api.o
i2c_is_ready_for_transaction_start                objs/i2c_api.o
i2c_master_scl_pinmap                             objs/i2c_api.o
i2c_master_sda_pinmap                             objs/i2c_api.o
i2c_read                                          objs/i2c_api.o
i2c_reset                                         objs/i2c_api.o
i2c_slave_scl_pinmap                              objs/i2c_api.o
i2c_slave_sda_pinmap                              objs/i2c_api.o
i2c_start                                         objs/i2c_api.o
i2c_stop                                          objs/i2c_api.o
i2c_sw_reset                                      objs/i2c_api.o
i2c_write                                         objs/i2c_api.o
init_32bit_timer                                  objs/us_ticker.o
init_uart                                         objs/serial_api.o
isrMessageGet                                     objs/rt_CMSIS.o
isrMessagePut                                     objs/rt_CMSIS.o
isrSemaphoreRelease                               objs/rt_CMSIS.o
isrSignalSet                                      objs/rt_CMSIS.o
isr_evt_set                                       objs/rt_Event.o
                                                  objs/rt_CMSIS.o
isr_mbx_receive                                   objs/rt_Mailbox.o
                                                  objs/rt_CMSIS.o
isr_mbx_send                                      objs/rt_Mailbox.o
                                                  objs/rt_CMSIS.o
isr_sem_send                                      objs/rt_Semaphore.o
                                                  objs/rt_CMSIS.o
kinetis_flash_erase_sector                        objs/port_stubs.o
                                                  objs/settings.o
kinetis_flash_read                                objs/port_stubs.o
kinetis_flash_write                               objs/port_stubs.o
                                                  objs/settings.o
ll_pin_defines                                    objs/pinmap.o
                                                  objs/gpio_api.o
m_tmr                                             objs/RTX_Conf_CM.o
                                                  objs/rt_Task.o
main                                              objs/gps_serial_test.o
                                                  objs/RTX_Conf_CM.o
mbed_assert_internal                              objs/board_fault.o
                                                  objs/i2c_api.o
                                                  objs/serial_device.o
                                                  objs/serial_api.o
                                                  objs/pinmap.o
                                                  objs/gpio_api.o
                                                  objs/analogin_device.o
mcu_sleep                                         objs/mcu_sleep.o
memcpy                                            objs/libc.o
                                                  objs/settings.o
                                                  objs/serial_api.o
                                                  objs/uid.o
                                                  objs/event.o
memset                                            objs/libc.o
                                                  objs/settings.o
                                                  objs/i2c_api.o
                                                  objs/serial_api.o
                                                  objs/uid.o
                                                  objs/system_file.o
                                                  objs/rtc_api.o
modem_setting                                     objs/settings.o
modem_setting_dump                                objs/settings.o
modem_settings_init                               objs/settings.o
mp_stk                                            objs/RTX_Conf_CM.o
                                                  objs/rt_Task.o
mp_stk_size                                       objs/RTX_Conf_CM.o
                                                  objs/rt_Task.o
mp_tcb                                            objs/RTX_Conf_CM.o
                                                  objs/rt_Task.o
mp_tcb_size                                       objs/RTX_Conf_CM.o
                                                  objs/rt_Task.o
mp_tmr_size                                       objs/RTX_Conf_CM.o
                                                  objs/rt_Task.o
nap_setting                                       objs/settings.o
nap_setting_dump                                  objs/settings.o
nap_settings_init                                 objs/settings.o
nmea_digit2dec                                    objs/nmea.o
nmea_fusedata                                     objs/nmea.o
                                                  objs/gps_serial_test.o
                                                  objs/gps_quectel.o
nmea_init                                         objs/nmea.o
                                                  objs/gps_serial_test.o
                                                  objs/gps_quectel.o
nmea_parsedata                                    objs/nmea.o
osDelay                                           objs/rt_CMSIS.o
                                                  objs/gps_quectel.o
osKernelInitialize                                objs/rt_CMSIS.o
                                                  objs/start.o
                                                  objs/RTX_Conf_CM.o
osKernelRunning                                   objs/rt_CMSIS.o
osKernelStart                                     objs/rt_CMSIS.o
                                                  objs/start.o
                                                  objs/RTX_Conf_CM.o
osKernelSysTick                                   objs/rt_CMSIS.o
osMailAlloc                                       objs/rt_CMSIS.o
osMailCAlloc                                      objs/rt_CMSIS.o
osMailCreate                                      objs/rt_CMSIS.o
osMailFree                                        objs/rt_CMSIS.o
osMailGet                                         objs/rt_CMSIS.o
osMailPut                                         objs/rt_CMSIS.o
osMessageCreate                                   objs/rt_CMSIS.o
osMessageGet                                      objs/rt_CMSIS.o
osMessagePut                                      objs/rt_CMSIS.o
osMessageQId_osTimerMessageQ                      objs/RTX_Conf_CM.o
                                                  objs/rt_CMSIS.o
osMutexCreate                                     objs/rt_CMSIS.o
                                                  objs/event.o
                                                  objs/printf.o
osMutexDelete                                     objs/rt_CMSIS.o
osMutexRelease                                    objs/rt_CMSIS.o
                                                  objs/event.o
                                                  objs/printf.o
osMutexWait                                       objs/rt_CMSIS.o
                                                  objs/event.o
                                                  objs/printf.o
osPoolAlloc                                       objs/rt_CMSIS.o
osPoolCAlloc                                      objs/rt_CMSIS.o
osPoolCreate                                      objs/rt_CMSIS.o
osPoolFree                                        objs/rt_CMSIS.o
osSemaphoreCreate                                 objs/rt_CMSIS.o
osSemaphoreDelete                                 objs/rt_CMSIS.o
osSemaphoreRelease                                objs/rt_CMSIS.o
osSemaphoreWait                                   objs/rt_CMSIS.o
osSignalClear                                     objs/rt_CMSIS.o
osSignalSet                                       objs/rt_CMSIS.o
                                                  objs/serial_device.o
                                                  objs/alarm.o
osSignalWait                                      objs/rt_CMSIS.o
                                                  objs/serial_api.o
osThreadCreate                                    objs/rt_CMSIS.o
                                                  objs/start.o
                                                  objs/RTX_Conf_CM.o
osThreadExit                                      objs/rt_CMSIS.o
osThreadGetId                                     objs/rt_CMSIS.o
                                                  objs/serial_api.o
                                                  objs/alarm.o
                                                  objs/libc.o
osThreadGetPriority                               objs/rt_CMSIS.o
osThreadId_osTimerThread                          objs/RTX_Conf_CM.o
                                                  objs/rt_CMSIS.o
osThreadSetPriority                               objs/rt_CMSIS.o
osThreadTerminate                                 objs/rt_CMSIS.o
osThreadYield                                     objs/rt_CMSIS.o
osTimerCall                                       objs/rt_CMSIS.o
osTimerCreate                                     objs/rt_CMSIS.o
osTimerDelete                                     objs/rt_CMSIS.o
osTimerStart                                      objs/rt_CMSIS.o
osTimerStop                                       objs/rt_CMSIS.o
osTimerThread                                     objs/rt_CMSIS.o
                                                  objs/RTX_Conf_CM.o
osWait                                            objs/rt_CMSIS.o
os_active_TCB                                     objs/RTX_Conf_CM.o
                                                  objs/rt_Task.o
                                                  objs/rt_Event.o
                                                  objs/rt_CMSIS.o
os_clockrate                                      objs/RTX_Conf_CM.o
                                                  objs/rt_CMSIS.o
os_dly                                            objs/rt_List.o
                                                  objs/rt_Task.o
                                                  objs/rt_System.o
os_error                                          objs/board_fault.o
                                                  objs/rt_System.o
                                                  objs/rt_Mailbox.o
                                                  objs/rt_List.o
                                                  objs/rt_CMSIS.o
os_fifo                                           objs/RTX_Conf_CM.o
                                                  objs/rt_Task.o
                                                  objs/rt_System.o
                                                  objs/rt_List.o
os_fifo_size                                      objs/RTX_Conf_CM.o
                                                  objs/rt_Task.o
os_flags                                          objs/RTX_Conf_CM.o
                                                  objs/rt_CMSIS.o
                                                  objs/HAL_CM4.o
os_idle_TCB                                       objs/rt_Task.o
os_idle_demon                                     objs/os_idle.o
                                                  objs/rt_Task.o
os_initialized                                    objs/rt_CMSIS.o
os_maxtaskrun                                     objs/RTX_Conf_CM.o
                                                  objs/rt_Task.o
os_messageQ_def_osTimerMessageQ                   objs/RTX_Conf_CM.o
                                                  objs/rt_CMSIS.o
os_messageQ_q_osTimerMessageQ                     objs/RTX_Conf_CM.o
os_mutex_cb_event_display_mutex                   objs/event.o
os_mutex_cb_printf_mutex                          objs/printf.o
os_mutex_def_event_display_mutex                  objs/event.o
os_mutex_def_printf_mutex                         objs/printf.o
os_rdy                                            objs/rt_List.o
                                                  objs/rt_Task.o
                                                  objs/rt_System.o
                                                  objs/rt_Semaphore.o
                                                  objs/rt_Robin.o
                                                  objs/rt_Mutex.o
                                                  objs/rt_Mailbox.o
                                                  objs/rt_Event.o
os_resume                                         objs/rt_CMSIS.o
os_robin                                          objs/rt_Robin.o
                                                  objs/rt_System.o
os_rrobin                                         objs/RTX_Conf_CM.o
                                                  objs/rt_Robin.o
os_running                                        objs/rt_CMSIS.o
os_set_env                                        objs/HAL_CM4.o
os_stack_mem                                      objs/RTX_Conf_CM.o
                                                  objs/rt_CMSIS.o
os_stack_sz                                       objs/RTX_Conf_CM.o
                                                  objs/rt_CMSIS.o
os_stackinfo                                      objs/RTX_Conf_CM.o
                                                  objs/rt_Task.o
                                                  objs/HAL_CM.o
os_suspend                                        objs/rt_CMSIS.o
os_thread_def_main                                objs/RTX_Conf_CM.o
                                                  objs/start.o
os_thread_def_osTimerThread                       objs/RTX_Conf_CM.o
                                                  objs/rt_CMSIS.o
os_tick_init                                      objs/rt_System.o
                                                  objs/rt_Task.o
os_tick_irqack                                    objs/rt_System.o
                                                  objs/HAL_CM4.o
os_tick_irqn                                      objs/rt_System.o
                                                  objs/rt_Task.o
os_tick_ovf                                       objs/rt_System.o
                                                  objs/rt_CMSIS.o
os_tick_val                                       objs/rt_System.o
                                                  objs/rt_CMSIS.o
os_tickfreq                                       objs/RTX_Conf_CM.o
os_tickus_f                                       objs/RTX_Conf_CM.o
os_tickus_i                                       objs/RTX_Conf_CM.o
os_time                                           objs/rt_Time.o
                                                  objs/port_stubs.o
                                                  objs/rt_System.o
                                                  objs/rt_Robin.o
                                                  objs/rt_List.o
                                                  objs/rt_CMSIS.o
os_timer_head                                     objs/rt_CMSIS.o
os_timernum                                       objs/RTX_Conf_CM.o
os_tmr                                            objs/RTX_Conf_CM.o
os_trv                                            objs/RTX_Conf_CM.o
                                                  objs/rt_System.o
                                                  objs/rt_CMSIS.o
os_tsk                                            objs/rt_Task.o
                                                  objs/rt_Time.o
                                                  objs/rt_System.o
                                                  objs/rt_Semaphore.o
                                                  objs/rt_Mutex.o
                                                  objs/rt_Mailbox.o
                                                  objs/rt_List.o
                                                  objs/rt_Event.o
                                                  objs/rt_CMSIS.o
                                                  objs/HAL_CM4.o
parking_setting                                   objs/settings.o
parking_setting_dump                              objs/settings.o
parking_settings_init                             objs/settings.o
pin_function                                      objs/pinmap.o
                                                  objs/serial_api_stubs.o
                                                  objs/i2c_api.o
                                                  objs/serial_api.o
                                                  objs/mbed_pinmap_common.o
                                                  objs/gpio_api.o
                                                  objs/analogin_device.o
pin_lines_desc                                    objs/gpio_irq_device.o
                                                  objs/gpio_irq_api.o
pin_mode                                          objs/pinmap.o
                                                  objs/i2c_api.o
                                                  objs/serial_api.o
                                                  objs/mbed_pinmap_common.o
                                                  objs/gpio_api.o
                                                  objs/analogin_device.o
pinmap_find_function                              objs/mbed_pinmap_common.o
                                                  objs/i2c_api.o
                                                  objs/serial_api.o
                                                  objs/analogin_device.o
pinmap_find_peripheral                            objs/mbed_pinmap_common.o
pinmap_find_peripheral_pins                       objs/mbed_pinmap_common.o
pinmap_function                                   objs/mbed_pinmap_common.o
pinmap_list_has_peripheral                        objs/mbed_pinmap_common.o
pinmap_list_has_pin                               objs/mbed_pinmap_common.o
pinmap_merge                                      objs/mbed_pinmap_common.o
                                                  objs/i2c_api.o
                                                  objs/serial_api.o
pinmap_peripheral                                 objs/mbed_pinmap_common.o
                                                  objs/i2c_api.o
                                                  objs/serial_api.o
                                                  objs/analogin_device.o
pinmap_pinout                                     objs/mbed_pinmap_common.o
                                                  objs/i2c_api.o
                                                  objs/serial_api.o
pop_registers_from_fault_stack                    objs/board_fault.o
port_delay_ms                                     objs/port_stubs.o
                                                  objs/serial_api_stubs.o
printf                                            objs/printf.o
                                                  objs/gps_serial_test.o
                                                  objs/settings.o
                                                  objs/gps_quectel.o
                                                  objs/serial_api.o
                                                  objs/system_file.o
                                                  objs/event.o
                                                  objs/console.o
                                                  objs/datetime.o
printf_init                                       objs/printf.o
printf_mutex                                      objs/printf.o
printf_put                                        objs/printf.o
printf_putchar                                    objs/printf.o
provision_setting                                 objs/settings.o
provision_setting_dump                            objs/settings.o
puts                                              objs/printf.o
rand                                              objs/libc.o
random_seed                                       objs/libc.o
random_setup_seed                                 objs/libc.o
reboot_setting                                    objs/settings.o
reboot_setting_dump                               objs/settings.o
reboot_settings_init                              objs/settings.o
reset_setting                                     objs/settings.o
restore_timer_ctx                                 objs/us_ticker.o
rt_alloc_box                                      objs/rt_MemBox.o
                                                  objs/rt_Task.o
                                                  objs/rt_Mailbox.o
                                                  objs/rt_CMSIS.o
                                                  objs/HAL_CM4.o
rt_alloc_mem                                      objs/rt_Memory.o
                                                  objs/rt_CMSIS.o
rt_block                                          objs/rt_Task.o
                                                  objs/rt_Time.o
                                                  objs/rt_Semaphore.o
                                                  objs/rt_Mutex.o
                                                  objs/rt_Mailbox.o
                                                  objs/rt_Event.o
                                                  objs/rt_CMSIS.o
rt_chk_robin                                      objs/rt_Robin.o
                                                  objs/rt_System.o
rt_dec_dly                                        objs/rt_List.o
                                                  objs/rt_System.o
rt_dispatch                                       objs/rt_Task.o
                                                  objs/rt_Semaphore.o
                                                  objs/rt_Mutex.o
                                                  objs/rt_Mailbox.o
                                                  objs/rt_Event.o
                                                  objs/rt_CMSIS.o
rt_dly_wait                                       objs/rt_Time.o
                                                  objs/rt_CMSIS.o
rt_evt_clr                                        objs/rt_Event.o
                                                  objs/rt_CMSIS.o
rt_evt_get                                        objs/rt_Event.o
rt_evt_psh                                        objs/rt_Event.o
                                                  objs/rt_System.o
rt_evt_set                                        objs/rt_Event.o
                                                  objs/rt_CMSIS.o
rt_evt_wait                                       objs/rt_Event.o
                                                  objs/rt_CMSIS.o
rt_free_box                                       objs/rt_MemBox.o
                                                  objs/rt_Task.o
                                                  objs/rt_CMSIS.o
                                                  objs/HAL_CM4.o
rt_free_mem                                       objs/rt_Memory.o
                                                  objs/rt_CMSIS.o
rt_get_PSP                                        objs/HAL_CM4.o
                                                  objs/rt_Task.o
rt_get_first                                      objs/rt_List.o
                                                  objs/rt_Task.o
                                                  objs/rt_System.o
                                                  objs/rt_Semaphore.o
                                                  objs/rt_Robin.o
                                                  objs/rt_Mutex.o
                                                  objs/rt_Mailbox.o
                                                  objs/rt_CMSIS.o
rt_get_same_rdy_prio                              objs/rt_List.o
                                                  objs/rt_Task.o
rt_init_mem                                       objs/rt_Memory.o
                                                  objs/rt_CMSIS.o
rt_init_robin                                     objs/rt_Robin.o
                                                  objs/rt_Task.o
rt_init_stack                                     objs/HAL_CM.o
                                                  objs/rt_Task.o
rt_itv_set                                        objs/rt_Time.o
rt_itv_wait                                       objs/rt_Time.o
rt_mbx_check                                      objs/rt_Mailbox.o
                                                  objs/rt_CMSIS.o
rt_mbx_init                                       objs/rt_Mailbox.o
                                                  objs/rt_CMSIS.o
rt_mbx_psh                                        objs/rt_Mailbox.o
                                                  objs/rt_System.o
rt_mbx_send                                       objs/rt_Mailbox.o
                                                  objs/rt_CMSIS.o
rt_mbx_wait                                       objs/rt_Mailbox.o
                                                  objs/rt_CMSIS.o
rt_mut_delete                                     objs/rt_Mutex.o
                                                  objs/rt_CMSIS.o
rt_mut_init                                       objs/rt_Mutex.o
                                                  objs/rt_CMSIS.o
rt_mut_release                                    objs/rt_Mutex.o
                                                  objs/rt_CMSIS.o
rt_mut_wait                                       objs/rt_Mutex.o
                                                  objs/rt_CMSIS.o
rt_pop_req                                        objs/rt_System.o
                                                  objs/HAL_CM4.o
rt_psh_req                                        objs/rt_System.o
                                                  objs/rt_Semaphore.o
                                                  objs/rt_Mailbox.o
                                                  objs/rt_Event.o
                                                  objs/rt_CMSIS.o
rt_psq_enq                                        objs/rt_List.o
                                                  objs/rt_Semaphore.o
                                                  objs/rt_Mailbox.o
                                                  objs/rt_Event.o
                                                  objs/rt_CMSIS.o
rt_put_dly                                        objs/rt_List.o
                                                  objs/rt_Task.o
rt_put_prio                                       objs/rt_List.o
                                                  objs/rt_Task.o
                                                  objs/rt_Semaphore.o
                                                  objs/rt_Robin.o
                                                  objs/rt_Mutex.o
                                                  objs/rt_Mailbox.o
                                                  objs/rt_Event.o
                                                  objs/rt_CMSIS.o
rt_put_rdy_first                                  objs/rt_List.o
                                                  objs/rt_Task.o
                                                  objs/rt_System.o
rt_resort_prio                                    objs/rt_List.o
                                                  objs/rt_Task.o
                                                  objs/rt_Mutex.o
rt_resume                                         objs/rt_System.o
                                                  objs/rt_CMSIS.o
rt_ret_val                                        objs/HAL_CM.o
                                                  objs/rt_Task.o
                                                  objs/rt_Semaphore.o
                                                  objs/rt_Mutex.o
                                                  objs/rt_Mailbox.o
                                                  objs/rt_CMSIS.o
rt_ret_val2                                       objs/HAL_CM.o
                                                  objs/rt_Mailbox.o
                                                  objs/rt_Event.o
rt_rmv_dly                                        objs/rt_List.o
                                                  objs/rt_Task.o
                                                  objs/rt_Semaphore.o
                                                  objs/rt_Mutex.o
                                                  objs/rt_Mailbox.o
                                                  objs/rt_Event.o
                                                  objs/rt_CMSIS.o
rt_rmv_list                                       objs/rt_List.o
                                                  objs/rt_Task.o
rt_sem_delete                                     objs/rt_Semaphore.o
                                                  objs/rt_CMSIS.o
rt_sem_init                                       objs/rt_Semaphore.o
                                                  objs/rt_CMSIS.o
rt_sem_psh                                        objs/rt_Semaphore.o
                                                  objs/rt_System.o
rt_sem_send                                       objs/rt_Semaphore.o
                                                  objs/rt_CMSIS.o
rt_sem_wait                                       objs/rt_Semaphore.o
                                                  objs/rt_CMSIS.o
rt_set_PSP                                        objs/HAL_CM4.o
rt_stk_check                                      objs/rt_System.o
                                                  objs/rt_Task.o
                                                  objs/HAL_CM4.o
rt_suspend                                        objs/rt_System.o
                                                  objs/rt_CMSIS.o
rt_switch_req                                     objs/rt_Task.o
                                                  objs/rt_System.o
rt_sys_init                                       objs/rt_Task.o
                                                  objs/rt_CMSIS.o
rt_sys_start                                      objs/rt_Task.o
                                                  objs/rt_CMSIS.o
rt_systick                                        objs/rt_System.o
                                                  objs/HAL_CM4.o
rt_time_get                                       objs/rt_Time.o
rt_tsk_create                                     objs/rt_Task.o
                                                  objs/rt_CMSIS.o
rt_tsk_delete                                     objs/rt_Task.o
                                                  objs/rt_CMSIS.o
rt_tsk_lock                                       objs/rt_System.o
rt_tsk_pass                                       objs/rt_Task.o
                                                  objs/rt_CMSIS.o
rt_tsk_prio                                       objs/rt_Task.o
                                                  objs/rt_CMSIS.o
rt_tsk_self                                       objs/rt_Task.o
                                                  objs/rt_CMSIS.o
rt_tsk_unlock                                     objs/rt_System.o
rtc_clock                                         objs/port_stubs.o
rtc_free                                          objs/rtc_api.o
rtc_init                                          objs/rtc_api.o
rtc_isenabled                                     objs/rtc_api.o
rtc_read                                          objs/rtc_api.o
                                                  objs/gps_serial_test.o
                                                  objs/gps_quectel.o
                                                  objs/event.o
                                                  objs/libc.o
rtc_reset_time                                    objs/port_stubs.o
rtc_save                                          objs/port_stubs.o
                                                  objs/board_fault.o
rtc_write                                         objs/rtc_api.o
save_timer_ctx                                    objs/us_ticker.o
seconds_to_datetime                               objs/datetime.o
semihost_close                                    objs/semihost_api.o
semihost_connected                                objs/semihost_api.o
semihost_disabledebug                             objs/semihost_api.o
semihost_ensure                                   objs/semihost_api.o
semihost_exit                                     objs/semihost_api.o
semihost_flen                                     objs/semihost_api.o
semihost_istty                                    objs/semihost_api.o
semihost_open                                     objs/semihost_api.o
semihost_powerdown                                objs/semihost_api.o
semihost_read                                     objs/semihost_api.o
semihost_readc                                    objs/semihost_api.o
semihost_remove                                   objs/semihost_api.o
semihost_rename                                   objs/semihost_api.o
semihost_reset                                    objs/semihost_api.o
semihost_seek                                     objs/semihost_api.o
semihost_uid                                      objs/semihost_api.o
semihost_vbus                                     objs/semihost_api.o
semihost_write                                    objs/semihost_api.o
                                                  objs/serial_api_stubs.o
semihost_writec                                   objs/semihost_api.o
serial_available                                  objs/serial_api_stubs.o
                                                  objs/console.o
serial_baud                                       objs/serial_api.o
                                                  objs/gps_quectel.o
serial_break_clear                                objs/serial_api.o
serial_break_set                                  objs/serial_device.o
serial_buffer                                     objs/serial_api_stubs.o
                                                  objs/gps_quectel.o
serial_clear                                      objs/serial_device.o
serial_console_init                               objs/serial_api_stubs.o
serial_cts_pinmap                                 objs/serial_api.o
serial_enable_dma_rx                              objs/serial_api_stubs.o
serial_enable_dma_tx                              objs/serial_api_stubs.o
serial_flush                                      objs/serial_api_stubs.o
                                                  objs/gps_quectel.o
serial_format                                     objs/serial_api.o
                                                  objs/gps_quectel.o
serial_free                                       objs/serial_api.o
                                                  objs/serial_api_stubs.o
serial_getc                                       objs/serial_device.o
serial_hold                                       objs/serial_api_stubs.o
serial_init                                       objs/serial_api.o
                                                  objs/gps_quectel.o
                                                  objs/serial_api_stubs.o
serial_irq_handler                                objs/serial_device.o
serial_irq_handler_asynch                         objs/serial_device.o
serial_irq_ids                                    objs/serial_device.o
                                                  objs/serial_api.o
serial_irq_set                                    objs/serial_device.o
serial_is_tx_ongoing                              objs/serial_api.o
serial_line_mode                                  objs/serial_api_stubs.o
                                                  objs/gps_quectel.o
serial_objects                                    objs/serial_api.o
                                                  objs/serial_device.o
serial_pinout_tx                                  objs/serial_api.o
serial_power_off                                  objs/serial_api_stubs.o
                                                  objs/mcu_sleep.o
serial_power_on                                   objs/serial_api_stubs.o
                                                  objs/gps_serial_test.o
                                                  objs/gps_quectel.o
serial_putc                                       objs/serial_device.o
serial_read                                       objs/serial_api.o
                                                  objs/console.o
serial_read_signal                                objs/serial_api.o
serial_read_timeout                               objs/serial_api.o
                                                  objs/gps_serial_test.o
                                                  objs/gps_quectel.o
serial_read_timeout_signal                        objs/serial_api.o
serial_readable                                   objs/serial_api.o
                                                  objs/serial_device.o
serial_release                                    objs/serial_api_stubs.o
serial_resume_dma_rx                              objs/serial_api_stubs.o
serial_rts_pinmap                                 objs/serial_api.o
serial_rx_abort_asynch                            objs/serial_device.o
serial_rx_active                                  objs/serial_device.o
serial_rx_asynch                                  objs/serial_device.o
serial_rx_pinmap                                  objs/serial_api.o
serial_semihost                                   objs/serial_api_stubs.o
serial_suspend_dma_rx                             objs/serial_api_stubs.o
serial_tx_abort_asynch                            objs/serial_device.o
serial_tx_active                                  objs/serial_device.o
serial_tx_asynch                                  objs/serial_device.o
serial_tx_pinmap                                  objs/serial_api.o
serial_writable                                   objs/serial_api.o
                                                  objs/serial_device.o
serial_write                                      objs/serial_api.o
                                                  objs/gps_quectel.o
                                                  objs/printf.o
serial_write_direct                               objs/serial_api_stubs.o
                                                  objs/printf.o
set_parse_line                                    objs/settings.o
set_parse_ptr                                     objs/settings.o
set_parse_size                                    objs/settings.o
set_uint16                                        objs/bits.o
set_uint32                                        objs/bits.o
set_uint64                                        objs/bits.o
set_us_ticker_irq_handler                         objs/mbed_us_ticker_api.o
setting_buffer                                    objs/settings.o
setting_commands                                  objs/settings.o
setting_get_char                                  objs/settings.o
setting_parse_line                                objs/settings.o
settings                                          objs/settings.o
                                                  objs/event.o
settings_dump                                     objs/settings.o
settings_execute                                  objs/settings.o
settings_find_key                                 objs/settings.o
settings_init                                     objs/settings.o
settings_init_all                                 objs/settings.o
settings_parse                                    objs/settings.o
settings_store                                    objs/settings.o
software_init_hook                                objs/RTX_Conf_CM.o
sprintf                                           objs/printf.o
                                                  objs/settings.o
                                                  objs/gps_quectel.o
                                                  objs/event.o
stdio_uart                                        objs/serial_api.o
stdio_uart_inited                                 objs/serial_api.o
stm_flash_device                                  objs/port_stubs.o
stm_flash_erase_sector                            objs/port_stubs.o
stm_flash_keep_on                                 objs/port_stubs.o
stm_flash_read                                    objs/port_stubs.o
stm_flash_write                                   objs/port_stubs.o
store_uint16                                      objs/bits.o
                                                  objs/event.o
store_uint32                                      objs/bits.o
                                                  objs/event.o
str_to_uid                                        objs/uid.o
strcmp                                            objs/libc.o
                                                  objs/settings.o
                                                  objs/nmea.o
                                                  objs/gps_quectel.o
                                                  objs/event.o
strcpy                                            objs/libc.o
strlen                                            objs/libc.o
                                                  objs/event.o
                                                  objs/semihost_api.o
strncmp                                           objs/libc.o
                                                  objs/nmea.o
strnlen                                           objs/libc.o
                                                  objs/printf.o
svcDelay                                          objs/rt_CMSIS.o
svcKernelInitialize                               objs/rt_CMSIS.o
svcKernelRunning                                  objs/rt_CMSIS.o
svcKernelStart                                    objs/rt_CMSIS.o
svcKernelSysTick                                  objs/rt_CMSIS.o
svcMailCreate                                     objs/rt_CMSIS.o
svcMessageCreate                                  objs/rt_CMSIS.o
svcMessageGet                                     objs/rt_CMSIS.o
svcMessagePut                                     objs/rt_CMSIS.o
svcMutexCreate                                    objs/rt_CMSIS.o
svcMutexDelete                                    objs/rt_CMSIS.o
svcMutexRelease                                   objs/rt_CMSIS.o
svcMutexWait                                      objs/rt_CMSIS.o
svcPoolCreate                                     objs/rt_CMSIS.o
svcSemaphoreCreate                                objs/rt_CMSIS.o
svcSemaphoreDelete                                objs/rt_CMSIS.o
svcSemaphoreRelease                               objs/rt_CMSIS.o
svcSemaphoreWait                                  objs/rt_CMSIS.o
svcSignalClear                                    objs/rt_CMSIS.o
svcSignalSet                                      objs/rt_CMSIS.o
svcSignalWait                                     objs/rt_CMSIS.o
svcThreadCreate                                   objs/rt_CMSIS.o
svcThreadGetId                                    objs/rt_CMSIS.o
                                                  objs/board_fault.o
svcThreadGetPriority                              objs/rt_CMSIS.o
svcThreadSetPriority                              objs/rt_CMSIS.o
svcThreadTerminate                                objs/rt_CMSIS.o
svcThreadYield                                    objs/rt_CMSIS.o
svcTimerCall                                      objs/rt_CMSIS.o
svcTimerCreate                                    objs/rt_CMSIS.o
svcTimerDelete                                    objs/rt_CMSIS.o
svcTimerStart                                     objs/rt_CMSIS.o
svcTimerStop                                      objs/rt_CMSIS.o
sysMailAlloc                                      objs/rt_CMSIS.o
sysMailFree                                       objs/rt_CMSIS.o
sysPoolAlloc                                      objs/rt_CMSIS.o
sysPoolFree                                       objs/rt_CMSIS.o
sysTimerTick                                      objs/rt_CMSIS.o
                                                  objs/rt_System.o
sysUserTimerUpdate                                objs/rt_CMSIS.o
                                                  objs/rt_System.o
sysUserTimerWakeupTime                            objs/rt_CMSIS.o
                                                  objs/rt_System.o
sys_file_backup_init                              objs/system_file.o
sys_file_backup_read_reg                          objs/system_file.o
sys_file_backup_write_reg                         objs/system_file.o
sys_file_clear                                    objs/system_file.o
sys_file_init                                     objs/system_file.o
                                                  objs/board_fault.o
sys_file_sync_to_backup                           objs/system_file.o
                                                  objs/board_fault.o
sys_reg_file_ptr                                  objs/system_file.o
                                                  objs/board_fault.o
test_gps_thread_subset                            objs/gps_serial_test.o
ticker_irq_handler                                objs/mbed_us_ticker_api.o
timer_ccr1_reg                                    objs/us_ticker.o
timer_cnt_reg                                     objs/us_ticker.o
timer_dier_reg                                    objs/us_ticker.o
timer_irq_handler                                 objs/us_ticker.o
timezone_offset                                   objs/datetime.o
truncf                                            /usr/lib/gcc/arm-none-eabi/13.2.1/../../../arm-none-eabi/lib/thumb/v7e-m+fp/hard/libm.a(libm_a-sf_trunc.o)
                                                  objs/nmea.o
uart_handlers                                     objs/serial_device.o
                                                  objs/serial_api_stubs.o
                                                  objs/serial_api.o
uart_printf                                       objs/printf.o
                                                  objs/announce.o
                                                  objs/board_fault.o
uart_putchar                                      objs/printf.o
uid_clear                                         objs/uid.o
uid_copy                                          objs/uid.o
uid_init                                          objs/uid.o
uid_match                                         objs/uid.o
uid_match_me                                      objs/uid.o
uid_set_me                                        objs/uid.o
us_ticker_clear_interrupt                         objs/us_ticker.o
                                                  objs/mbed_us_ticker_api.o
us_ticker_disable_interrupt                       objs/us_ticker.o
                                                  objs/mbed_us_ticker_api.o
us_ticker_fire_interrupt                          objs/us_ticker.o
                                                  objs/mbed_us_ticker_api.o
us_ticker_free                                    objs/us_ticker.o
                                                  objs/mbed_us_ticker_api.o
us_ticker_get_info                                objs/us_ticker.o
                                                  objs/mbed_us_ticker_api.o
us_ticker_init                                    objs/us_ticker.o
                                                  objs/mbed_us_ticker_api.o
us_ticker_irq_handler                             objs/mbed_us_ticker_api.o
                                                  objs/us_ticker.o
us_ticker_read                                    objs/us_ticker.o
                                                  objs/serial_api_stubs.o
                                                  objs/mbed_us_ticker_api.o
                                                  objs/wait_api.o
us_ticker_set_interrupt                           objs/us_ticker.o
                                                  objs/mbed_us_ticker_api.o
uwTick                                            objs/stm32l4xx_hal.o
uwTickFreq                                        objs/stm32l4xx_hal.o
uwTickPrio                                        objs/stm32l4xx_hal.o
                                                  objs/stm32l4xx_hal_rcc.o
vsnprintf                                         objs/serial_wire_debug.o
vsprintf                                          objs/printf.o
                                                  objs/serial_api.o
vsprintf_common                                   objs/printf.o
wait                                              objs/wait_api.o
wait_ms                                           objs/wait_api.o
                                                  objs/port_stubs.o
wait_us                                           objs/wait_api.o
                                                  objs/i2c_api.o
