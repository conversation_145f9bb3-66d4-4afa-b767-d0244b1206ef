/**
 * @file time_functions_test.c
 * @brief Test suite to verify which time functions actually return incrementing values
 * 
 * This test suite checks all time-related functions to determine which ones
 * are actually working and returning incrementing values vs. those that are
 * stuck at zero or constant values.
 */

#include <stdio.h>
#include <stdint.h>
#include <stdbool.h>
#include <string.h>

// Include all the timing headers
#include "pelagic.h"
#include "rtc_api.h"
#include "us_ticker_api.h"
#include "wait_api.h"
#include "datetime.h"
#include "alarm.h"
#include "port_stubs.h"

#include "console.h"
#include "system_file.h"
#include "semihost_api.h"

// RTX includes
#include "rt_Time.h"
#include "cmsis_os.h"

// HAL includes
#include "stm32l4xx_hal.h"

// Test configuration
#define TEST_ITERATIONS 10
#define TEST_DELAY_MS 100
#define MIN_EXPECTED_INCREMENT 1  // Minimum increment we expect to see

// Test results structure
typedef struct {
    const char* function_name;
    bool is_working;
    uint32_t initial_value;
    uint32_t final_value;
    uint32_t max_increment;
    const char* notes;
} test_result_t;

// Global test results array
static test_result_t test_results[20];
static int test_count = 0;


// externs
void random_setup_seed(void);
void printf_init(void);





/**
 * @brief Run the complete time functions test suite
 * 
 * This function tests all time-related functions to determine which ones
 * are actually working and returning incrementing values.
 * 
 * @return 0 if at least one time function is working, 1 if all are broken
 */
int run_time_functions_test(void);

// Helper function to add a test result
static void add_test_result(const char* name, bool working, uint32_t initial, 
                           uint32_t final, uint32_t max_inc, const char* notes) {
    if (test_count < 20) {
        test_results[test_count].function_name = name;
        test_results[test_count].is_working = working;
        test_results[test_count].initial_value = initial;
        test_results[test_count].final_value = final;
        test_results[test_count].max_increment = max_inc;
        test_results[test_count].notes = notes;
        test_count++;
    }
}

// Reliable delay function using RTX os_time (which we know works)
static void reliable_delay_ms(uint32_t ms) {
    extern U32 os_time;
    U32 start_time = os_time;
    U32 target_ticks = ms;  // os_time increments every 1ms

    while ((os_time - start_time) < target_ticks) {
        // Wait using reliable RTX timing
        osDelay(1);  // 1ms RTX delay
    }
}

// Helper function to test a time function
static bool test_time_function(const char* name, uint32_t (*func)(void),
                              uint32_t expected_min_increment, const char* notes) {
    uint32_t values[TEST_ITERATIONS];
    uint32_t max_increment = 0;
    bool is_incrementing = false;

    printf("Testing %s...\n", name);

    // Collect samples with delays between them
    for (int i = 0; i < TEST_ITERATIONS; i++) {
        values[i] = func();
        printf("  Sample %d: %lu\n", i, values[i]);

        if (i > 0) {
            uint32_t increment = values[i] - values[i-1];
            if (increment > max_increment) {
                max_increment = increment;
            }
            if (increment >= expected_min_increment) {
                is_incrementing = true;
            }
        }

        // Add delay between samples (except last iteration) using reliable timing
        if (i < TEST_ITERATIONS - 1) {
            reliable_delay_ms(TEST_DELAY_MS);
        }
    }

    add_test_result(name, is_incrementing, values[0], values[TEST_ITERATIONS-1],
                   max_increment, notes);

    printf("  Result: %s (max increment: %lu)\n\n",
           is_incrementing ? "WORKING" : "FAILED", max_increment);

    return is_incrementing;
}

// Test HAL_GetTick - this should fail if uwTick never increments
static bool test_hal_get_tick(void) {
    printf("Testing HAL_GetTick (checking for uwTick increment issue)...\n");

    // Check the underlying uwTick variable directly
    extern __IO uint32_t uwTick;
    uint32_t uwTick_initial = uwTick;

    // Take multiple HAL_GetTick samples with delays using reliable timing
    uint32_t hal_values[TEST_ITERATIONS];
    for (int i = 0; i < TEST_ITERATIONS; i++) {
        hal_values[i] = HAL_GetTick();
        printf("  HAL_GetTick sample %d: %lu (uwTick: %lu)\n", i, hal_values[i], uwTick);
        if (i < TEST_ITERATIONS - 1) {
            reliable_delay_ms(TEST_DELAY_MS);  // Use reliable delay instead of wait_ms
        }
    }

    uint32_t uwTick_final = uwTick;
    bool uwTick_incremented = (uwTick_final > uwTick_initial);
    bool hal_incremented = (hal_values[TEST_ITERATIONS-1] > hal_values[0]);

    printf("  uwTick: %lu -> %lu (%s)\n", uwTick_initial, uwTick_final,
           uwTick_incremented ? "INCREMENTED" : "STUCK");
    printf("  HAL_GetTick: %lu -> %lu (%s)\n", hal_values[0], hal_values[TEST_ITERATIONS-1],
           hal_incremented ? "INCREMENTED" : "STUCK");

    // This test specifically fails if HAL_GetTick always returns zero
    if (!hal_incremented && hal_values[0] == 0) {
        printf("  *** DETECTED: HAL_GetTick stuck at zero - uwTick not incrementing! ***\n");
    }

    add_test_result("HAL_GetTick", hal_incremented, hal_values[0],
                   hal_values[TEST_ITERATIONS-1],
                   hal_values[TEST_ITERATIONS-1] - hal_values[0],
                   uwTick_incremented ? "HAL tick working" : "uwTick stuck - SysTick broken");

    printf("  Result: %s\n\n", hal_incremented ? "WORKING" : "FAILED");

    return hal_incremented;
}

// Test us_ticker_read
static bool test_us_ticker_read(void) {
    return test_time_function("us_ticker_read", us_ticker_read, 1000, 
                             "Should increment in microseconds");
}

// Test rt_time_get
static bool test_rt_time_get(void) {
    return test_time_function("rt_time_get", rt_time_get, 1, 
                             "RTX system tick counter");
}

// Test rtc_read
static bool test_rtc_read(void) {
    return test_time_function("rtc_read", (uint32_t(*)(void))rtc_read, 0, 
                             "RTC seconds since epoch");
}

// Test clock_read
static bool test_clock_read(void) {
    return test_time_function("clock_read", clock_read, 0, 
                             "Software epoch clock");
}

// Test timer interrupt functionality
static bool test_timer_interrupts(void) {
    printf("Testing timer interrupt functionality...\n");

    // Check if HAL_IncTick is being called by monitoring uwTick
    extern __IO uint32_t uwTick;
    uint32_t uwTick_before = uwTick;

    // Wait for a reasonable time for interrupts to occur
    for (volatile int i = 0; i < 5000000; i++);  // Longer delay

    uint32_t uwTick_after = uwTick;
    bool hal_tick_interrupt_working = (uwTick_after > uwTick_before);

    printf("  uwTick before delay: %lu\n", uwTick_before);
    printf("  uwTick after delay: %lu\n", uwTick_after);
    printf("  HAL tick interrupts: %s\n", hal_tick_interrupt_working ? "WORKING" : "NOT WORKING");

    // Check RTX os_time
    extern U32 os_time;
    U32 os_time_before = os_time;

    for (volatile int i = 0; i < 5000000; i++);  // Another delay

    U32 os_time_after = os_time;
    bool rtx_tick_interrupt_working = (os_time_after > os_time_before);

    printf("  os_time before delay: %lu\n", os_time_before);
    printf("  os_time after delay: %lu\n", os_time_after);
    printf("  RTX tick interrupts: %s\n", rtx_tick_interrupt_working ? "WORKING" : "NOT WORKING");

    bool any_interrupt_working = hal_tick_interrupt_working || rtx_tick_interrupt_working;

    add_test_result("Timer Interrupts", any_interrupt_working,
                   uwTick_before + os_time_before, uwTick_after + os_time_after,
                   (uwTick_after - uwTick_before) + (os_time_after - os_time_before),
                   any_interrupt_working ? "Timer interrupts functioning" : "No timer interrupts detected");

    printf("  Result: %s\n\n", any_interrupt_working ? "WORKING" : "FAILED");

    return any_interrupt_working;
}

// Test global variables
static bool test_global_variables(void) {
    printf("Testing global time variables...\n");

    // Test os_time
    extern U32 os_time;
    U32 os_time_initial = os_time;
    reliable_delay_ms(TEST_DELAY_MS * 5);  // Use reliable delay
    U32 os_time_final = os_time;
    bool os_time_working = (os_time_final > os_time_initial);

    printf("  os_time: %lu -> %lu (%s)\n", os_time_initial, os_time_final,
           os_time_working ? "WORKING" : "FAILED");

    add_test_result("os_time", os_time_working, os_time_initial, os_time_final,
                   os_time_final - os_time_initial, "RTX global time counter");

    // Test rtc_clock
    extern volatile uint32_t rtc_clock;
    uint32_t rtc_clock_initial = rtc_clock;
    reliable_delay_ms(TEST_DELAY_MS * 5);  // Use reliable delay
    uint32_t rtc_clock_final = rtc_clock;
    bool rtc_clock_working = (rtc_clock_final > rtc_clock_initial);

    printf("  rtc_clock: %lu -> %lu (%s)\n", rtc_clock_initial, rtc_clock_final,
           rtc_clock_working ? "WORKING" : "FAILED");

    add_test_result("rtc_clock", rtc_clock_working, rtc_clock_initial, rtc_clock_final,
                   rtc_clock_final - rtc_clock_initial, "Software RTC counter");

    // Test epoch_clock
    extern volatile uint32_t epoch_clock;
    uint32_t epoch_clock_initial = epoch_clock;
    reliable_delay_ms(TEST_DELAY_MS * 5);  // Use reliable delay
    uint32_t epoch_clock_final = epoch_clock;
    bool epoch_clock_working = (epoch_clock_final > epoch_clock_initial);

    printf("  epoch_clock: %lu -> %lu (%s)\n", epoch_clock_initial, epoch_clock_final,
           epoch_clock_working ? "WORKING" : "FAILED");

    add_test_result("epoch_clock", epoch_clock_working, epoch_clock_initial, epoch_clock_final,
                   epoch_clock_final - epoch_clock_initial, "Software epoch counter");

    printf("\n");
    return os_time_working || rtc_clock_working || epoch_clock_working;
}

// Test SysTick configuration and functionality
static bool test_systick_functionality(void) {
    printf("Testing SysTick configuration and functionality...\n");

    // Check if SysTick is configured and running
    uint32_t systick_ctrl = SysTick->CTRL;
    uint32_t systick_load = SysTick->LOAD;
    uint32_t systick_val_1 = SysTick->VAL;

    // Wait a bit and check if VAL register changes
    for (volatile int i = 0; i < 1000; i++);
    uint32_t systick_val_2 = SysTick->VAL;

    printf("  SysTick->CTRL: 0x%08lX\n", systick_ctrl);
    printf("  SysTick->LOAD: %lu\n", systick_load);
    printf("  SysTick->VAL: %lu -> %lu\n", systick_val_1, systick_val_2);

    bool systick_enabled = (systick_ctrl & SysTick_CTRL_ENABLE_Msk) != 0;
    bool systick_tickint = (systick_ctrl & SysTick_CTRL_TICKINT_Msk) != 0;
    bool systick_counting = (systick_val_1 != systick_val_2);

    printf("  SysTick enabled: %s\n", systick_enabled ? "YES" : "NO");
    printf("  SysTick interrupt enabled: %s\n", systick_tickint ? "YES" : "NO");
    printf("  SysTick counting: %s\n", systick_counting ? "YES" : "NO");

    bool systick_working = systick_enabled && systick_counting;

    add_test_result("SysTick", systick_working, systick_val_1, systick_val_2,
                   systick_val_1 > systick_val_2 ? systick_val_1 - systick_val_2 : 0,
                   systick_working ? "SysTick configured and running" : "SysTick not working");

    printf("  Result: %s\n\n", systick_working ? "WORKING" : "FAILED");

    return systick_working;
}

// Test wait functions accuracy
static bool test_wait_functions(void) {
    printf("Testing wait function accuracy...\n");

    // Test wait_us accuracy using us_ticker_read if us_ticker is working
    uint32_t start_us = us_ticker_read();
    if (start_us == 0) {
        printf("  wait_us test skipped - us_ticker not working\n");
        add_test_result("wait_us", false, 0, 0, 0, "us_ticker not available");
        printf("\n");
        return false;
    }

    // Use reliable delay instead of wait_us to test timing accuracy
    reliable_delay_ms(10);  // 10ms delay using reliable RTX timing
    uint32_t end_us = us_ticker_read();
    uint32_t elapsed_us = end_us - start_us;

    bool timing_accurate = (elapsed_us >= 9000 && elapsed_us <= 15000);  // Allow 50% tolerance
    printf("  reliable_delay_ms(10): elapsed %lu us (%s)\n", elapsed_us,
           timing_accurate ? "ACCURATE" : "INACCURATE");

    add_test_result("reliable_delay", timing_accurate, start_us, end_us, elapsed_us,
                   "10ms delay accuracy test using RTX");

    printf("\n");
    return timing_accurate;
}

// Print summary report
static void print_summary(void) {
    printf("=== TIME FUNCTIONS TEST SUMMARY ===\n");
    printf("%-20s %-10s %-12s %-12s %-12s %s\n", 
           "Function", "Status", "Initial", "Final", "Max Inc", "Notes");
    printf("%-20s %-10s %-12s %-12s %-12s %s\n", 
           "--------", "------", "-------", "-----", "-------", "-----");
    
    int working_count = 0;
    for (int i = 0; i < test_count; i++) {
        printf("%-20s %-10s %-12lu %-12lu %-12lu %s\n",
               test_results[i].function_name,
               test_results[i].is_working ? "WORKING" : "FAILED",
               test_results[i].initial_value,
               test_results[i].final_value,
               test_results[i].max_increment,
               test_results[i].notes);
        
        if (test_results[i].is_working) {
            working_count++;
        }
    }
    
    printf("\nSummary: %d/%d functions are working correctly\n", working_count, test_count);
    
    if (working_count == 0) {
        printf("ERROR: No time functions are working! System timing is broken.\n");
    } else if (working_count < test_count) {
        printf("WARNING: Some time functions are not working correctly.\n");
    } else {
        printf("SUCCESS: All tested time functions are working correctly.\n");
    }
}

// Main test function with timeout protection
int run_time_functions_test(void) {
    printf("=== TIME FUNCTIONS TEST SUITE ===\n");
    printf("Testing all time-related functions for proper incrementing behavior\n");
    printf("Test delay between samples: %d ms\n", TEST_DELAY_MS);
    printf("Number of samples per function: %d\n\n", TEST_ITERATIONS);

    // Initialize timing systems
    printf("Initializing timing systems...\n");
    rtc_init();
    us_ticker_init();
    printf("Initialization complete.\n\n");

    // Run all tests with timeout protection
    bool any_working = false;
    extern U32 os_time;
    U32 test_start_time = os_time;
    const U32 TEST_TIMEOUT_MS = 60000;  // 60 second timeout for entire test suite

    printf("Starting tests (timeout: %lu seconds)...\n\n", TEST_TIMEOUT_MS / 1000);

    // Test in order of reliability (most reliable first)
    any_working |= test_systick_functionality();
    if ((os_time - test_start_time) > TEST_TIMEOUT_MS) goto timeout;

    any_working |= test_timer_interrupts();
    if ((os_time - test_start_time) > TEST_TIMEOUT_MS) goto timeout;

    any_working |= test_hal_get_tick();  // This is the problematic one
    if ((os_time - test_start_time) > TEST_TIMEOUT_MS) goto timeout;

    any_working |= test_us_ticker_read();
    if ((os_time - test_start_time) > TEST_TIMEOUT_MS) goto timeout;

    any_working |= test_rt_time_get();
    if ((os_time - test_start_time) > TEST_TIMEOUT_MS) goto timeout;

    any_working |= test_rtc_read();
    if ((os_time - test_start_time) > TEST_TIMEOUT_MS) goto timeout;

    any_working |= test_clock_read();
    if ((os_time - test_start_time) > TEST_TIMEOUT_MS) goto timeout;

    any_working |= test_global_variables();
    if ((os_time - test_start_time) > TEST_TIMEOUT_MS) goto timeout;

    any_working |= test_wait_functions();

    // Print summary
    print_summary();

    printf("Test suite completed in %lu ms\n", os_time - test_start_time);
    return any_working ? 0 : 1;  // 0 = success, 1 = failure

timeout:
    printf("\n*** TEST SUITE TIMEOUT after %lu ms ***\n", os_time - test_start_time);
    printf("Some tests may have hung - printing partial results:\n\n");
    print_summary();
    return 1;  // Timeout is a failure
}





//!------------------------------------------------------------------------------------



// Test configuration
#define TEST_REPEAT_INTERVAL_SEC 30

int main(void) {
    // Initialize system
    sys_file_init();
    
    SWO_Init();
    ITM_SendString("Hello from STM32L4 via SWO!\r\n");
    printf_init();
    
    printf("\n");
    printf("========================================\n");
    printf("  PELAGIC TIME FUNCTIONS TEST SUITE\n");
    printf("========================================\n");
    printf("Build: %s\n", __DATE__ " " __TIME__);
    printf("Target: STM32L431xC\n");
    printf("\n");
    
    // Run the test once immediately
    printf("Running initial test...\n");
    int result = run_time_functions_test();
    
    if (result == 0) {
        printf("\n*** TEST PASSED: At least one time function is working ***\n");
    } else {
        printf("\n*** TEST FAILED: No time functions are working! ***\n");
        printf("This indicates a serious timing system problem.\n");
    }
    
    printf("\nTest will repeat every %d seconds...\n", TEST_REPEAT_INTERVAL_SEC);
    printf("Press reset to restart or reflash to exit.\n");
    printf("========================================\n\n");
    
    // Continuous testing loop
    int test_iteration = 1;
    while (1) {
        // Wait for next test interval
        // Use a simple delay loop since we can't trust the timing functions
        for (volatile int i = 0; i < 10000000; i++) {
            // Simple delay loop - approximately 30 seconds on STM32L431
            // This is crude but doesn't rely on any timing functions
        }
        
        printf("\n--- Test Iteration %d ---\n", ++test_iteration);
        result = run_time_functions_test();
        
        if (result == 0) {
            printf("*** ITERATION %d: PASSED ***\n", test_iteration);
        } else {
            printf("*** ITERATION %d: FAILED ***\n", test_iteration);
        }
    }
    
    return 0;
}
