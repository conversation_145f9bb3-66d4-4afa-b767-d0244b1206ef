Hello from STM32L4 via SWO!

========================================
  PELAGIC TIME FUNCTIONS TEST SUITE
========================================
Build: Jun 24 2025 10:22:53
Target: STM32L431xC

Running initial test...
=== TIME FUNCTIONS TEST SUITE ===
Testing all time-related functions for proper incrementing behavior
Test delay between samples: 100 ms
Number of samples per function: 10

Initializing timing systems...
Initialization complete.

Starting tests (timeout: 60 seconds)...

Testing SysTick configuration and functionality...
  SysTick->CTRL: 0x00010007
  SysTick->LOAD: 20970
  SysTick->VAL: 12847 -> 3834
  SysTick enabled: YES
  SysTick interrupt enabled: YES
  SysTick counting: YES
  Result: WORKING

Testing timer interrupt functionality...
  uwTick before delay: 11
  uwTick after delay: 2676
  HAL tick interrupts: WORKING
  os_time before delay: 2677
  os_time after delay: 5342
  RTX tick interrupts: WORKING
  Result: WORKING

Testing HAL_GetTick (checking for uwTick increment issue)...
  HAL_GetTick sample 0: 5344 (uwTick: 5344)
  HAL_GetTick sample 1: 5444 (uwTick: 5444)
  HAL_GetTick sample 2: 5544 (uwTick: 5544)
  HAL_GetTick sample 3: 5644 (uwTick: 5644)
  HAL_GetTick sample 4: 5744 (uwTick: 5744)
  HAL_GetTick sample 5: 5844 (uwTick: 5844)
  HAL_GetTick sample 6: 5944 (uwTick: 5944)
  HAL_GetTick sample 7: 6044 (uwTick: 6044)
  HAL_GetTick sample 8: 6144 (uwTick: 6144)
  HAL_GetTick sample 9: 6244 (uwTick: 6244)
  uwTick: 5344 -> 6244 (INCREMENTED)
  HAL_GetTick: 5344 -> 6244 (INCREMENTED)
  Result: WORKING

Testing us_ticker_read...
  Sample 0: 0
  Sample 1: 0
  Sample 2: 0
  Sample 3: 0
  Sample 4: 0
  Sample 5: 0
  Sample 6: 0
  Sample 7: 0
  Sample 8: 0
  Sample 9: 0
  Result: FAILED (max increment: 0)

Testing rt_time_get...
  Sample 0: 7147
  Sample 1: 7247
  Sample 2: 7347
  Sample 3: 7447
  Sample 4: 7547
  Sample 5: 7647
  Sample 6: 7747
  Sample 7: 7847
  Sample 8: 7947
  Sample 9: 8047
  Result: WORKING (max increment: 100)

Testing rtc_read...
  Sample 0: 785480982
  Sample 1: 785480983
  Sample 2: 785480985
  Sample 3: 785480986
  Sample 4: 785480988
  Sample 5: 785480989
  Sample 6: 785480991
  Sample 7: 785480992
  Sample 8: 785480994
  Sample 9: 785480995
  Result: WORKING (max increment: 2)

Testing clock_read...
  Sample 0: 0
  Sample 1: 0
  Sample 2: 0
  Sample 3: 0
  Sample 4: 0
  Sample 5: 0
  Sample 6: 0
  Sample 7: 0
  Sample 8: 0
  Sample 9: 0
  Result: FAILED (max increment: 0)

Testing global time variables...
  os_time: 11710 -> 12210 (WORKING)
  rtc_clock: 0 -> 0 (FAILED)
  epoch_clock: 0 -> 0 (FAILED)

Testing wait function accuracy...
  wait_us test skipped - us_ticker not working

=== TIME FUNCTIONS TEST SUMMARY ===
Function             Status     Initial      Final        Max Inc      Notes
--------             ------     -------      -----        -------      -----
SysTick              WORKING    12847        3834         9013         SysTick configured and running
Timer Interrupts     WORKING    2688         8018         5330         Timer interrupts functioning
HAL_GetTick          WORKING    5344         6244         900          HAL tick working
us_ticker_read       FAILED     0            0            0            Should increment in microseconds
rt_time_get          WORKING    7147         8047         100          RTX system tick counter
rtc_read             WORKING    785480982    785480995    2            RTC seconds since epoch
clock_read           FAILED     0            0            0            Software epoch clock
os_time              WORKING    11710        12210        500          RTX global time counter
rtc_clock            FAILED     0            0            0            Software RTC counter
epoch_clock          FAILED     0            0            0            Software epoch counter
wait_us              FAILED     0            0            0            us_ticker not available

Summary: 6/11 functions are working correctly
WARNING: Some time functions are not working correctly.
Test suite completed in 13220 ms

*** TEST PASSED: At least one time function is working ***
