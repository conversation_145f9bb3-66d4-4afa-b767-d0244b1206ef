========================================
  PELAGIC TIME FUNCTIONS TEST SUITE
========================================
Build: Jun 23 2025 21:14:43
Target: STM32L431xC

Running initial test...
=== TIME FUNCTIONS TEST SUITE ===
Testing all time-related functions for proper incrementing behavior
Test delay between samples: 100 ms
Number of samples per function: 10

Initializing timing systems...
Initialization complete.

Starting tests (timeout: 60 seconds)...

Testing SysTick configuration and functionality...
  SysTick->CTRL: 0x00010007
  SysTick->LOAD: 20970
  SysTick->VAL: 15366 -> 6353
  SysTick enabled: YES
  SysTick interrupt enabled: YES
  SysTick counting: YES
  Result: WORKING

Testing timer interrupt functionality...
  uwTick before delay: 0
  uwTick after delay: 0
  HAL tick interrupts: NOT WORKING
  os_time before delay: 2674
  os_time after delay: 5335
  RTX tick interrupts: WORKING
  Result: WORKING

Testing HAL_GetTick (checking for uwTick increment issue)...
  HAL_GetTick sample 0: 0 (uwTick: 0)
  HAL_GetTick sample 1: 0 (uwTick: 0)
  HAL_GetTick sample 2: 0 (uwTick: 0)
  HAL_GetTick sample 3: 0 (uwTick: 0)
  HAL_GetTick sample 4: 0 (uwTick: 0)
  HAL_GetTick sample 5: 0 (uwTick: 0)
  HAL_GetTick sample 6: 0 (uwTick: 0)
  HAL_GetTick sample 7: 0 (uwTick: 0)
  HAL_GetTick sample 8: 0 (uwTick: 0)
  HAL_GetTick sample 9: 0 (uwTick: 0)
  uwTick: 0 -> 0 (STUCK)
  HAL_GetTick: 0 -> 0 (STUCK)
  *** DETECTED: HAL_GetTick stuck at zero - uwTick not incrementing! ***
  Result: FAILED

Testing us_ticker_read...
  Sample 0: 0
  Sample 1: 0
  Sample 2: 0
  Sample 3: 0
  Sample 4: 0
  Sample 5: 0
  Sample 6: 0
  Sample 7: 0
  Sample 8: 0
  Sample 9: 0
  Result: FAILED (max increment: 0)

Testing rt_time_get...
  Sample 0: 7141
  Sample 1: 7241
  Sample 2: 7341
  Sample 3: 7441
  Sample 4: 7541
  Sample 5: 7641
  Sample 6: 7741
  Sample 7: 7841
  Sample 8: 7941
  Sample 9: 8041
  Result: WORKING (max increment: 100)

Testing rtc_read...
  Sample 0: 0
  Sample 1: 0
  Sample 2: 0
  Sample 3: 0
  Sample 4: 0
  Sample 5: 0
  Sample 6: 0
  Sample 7: 0
  Sample 8: 0
  Sample 9: 0
  Result: WORKING (max increment: 0)

Testing clock_read...
  Sample 0: 0
  Sample 1: 0
  Sample 2: 0
  Sample 3: 0
  Sample 4: 0
  Sample 5: 0
  Sample 6: 0
  Sample 7: 0
  Sample 8: 0
  Sample 9: 0
  Result: WORKING (max increment: 0)

Testing global time variables...
  os_time: 9844 -> 10344 (WORKING)
  rtc_clock: 0 -> 0 (FAILED)
  epoch_clock: 0 -> 0 (FAILED)

Testing wait function accuracy...
  wait_us test skipped - us_ticker not working

=== TIME FUNCTIONS TEST SUMMARY ===
Function             Status     Initial      Final        Max Inc      Notes
--------             ------     -------      -----        -------      -----
SysTick              WORKING    15366        6353         9013         SysTick configured and running
Timer Interrupts     WORKING    2674         5335         2661         Timer interrupts functioning
HAL_GetTick          FAILED     0            0            0            uwTick stuck - SysTick broken
us_ticker_read       FAILED     0            0            0            Should increment in microseconds
rt_time_get          WORKING    7141         8041         100          RTX system tick counter
rtc_read             WORKING    0            0            0            RTC seconds since epoch
clock_read           WORKING    0            0            0            Software epoch clock
os_time              WORKING    9844         10344        500          RTX global time counter
rtc_clock            FAILED     0            0            0            Software RTC counter
epoch_clock          FAILED     0            0            0            Software epoch counter
wait_us              FAILED     0            0            0            us_ticker not available

Summary: 6/11 functions are working correctly
WARNING: Some time functions are not working correctly.
Test suite completed in 11354 ms

*** TEST PASSED: At least one time function is working ***